package com.qudaiji.cloud.p.module.system.convert.user;

import com.qudaiji.cloud.framework.common.util.collection.CollectionUtils;
import com.qudaiji.cloud.framework.common.util.collection.MapUtils;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.DeptSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.PostSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.response.RoleSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserProfileResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.PostDO;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.entity.SocialUserDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    default List<UserResponse> convertList(List<SystemUserDO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> convert(user, deptMap.get(user.getDeptId())));
    }

    default UserResponse convert(SystemUserDO user, DeptDO dept) {
        UserResponse userVO = BeanUtils.toBean(user, UserResponse.class);
        if (dept != null) {
            userVO.setDeptName(dept.getName());
        }
        return userVO;
    }

    default List<UserSimpleResponse> convertSimpleList(List<SystemUserDO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> {
            UserSimpleResponse userVO = BeanUtils.toBean(user, UserSimpleResponse.class);
            MapUtils.findAndThen(deptMap, user.getDeptId(), dept -> userVO.setDeptName(dept.getName()));
            return userVO;
        });
    }

    default UserProfileResponse convert(SystemUserDO user, List<RoleDO> userRoles,
                                        DeptDO dept, List<PostDO> posts, List<SocialUserDO> socialUsers) {
        UserProfileResponse userVO = BeanUtils.toBean(user, UserProfileResponse.class);
        userVO.setRoles(BeanUtils.toBean(userRoles, RoleSimpleResponse.class));
        userVO.setDept(BeanUtils.toBean(dept, DeptSimpleResponse.class));
        userVO.setPosts(BeanUtils.toBean(posts, PostSimpleResponse.class));
        userVO.setSocialUsers(BeanUtils.toBean(socialUsers, UserProfileResponse.SocialUser.class));
        return userVO;
    }

}
