package com.qudaiji.cloud.p.module.system.controller.admin.auth.request;

import com.qudaiji.cloud.framework.common.validation.InEnum;
import com.qudaiji.cloud.framework.common.validation.Mobile;
import com.qudaiji.cloud.p.common.constants.enums.sms.SmsSceneEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理后台 - 发送手机验证码 Request
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthSmsSendRequest {

    /**
     * 手机号
     * 例如：jqm-p-serviceyuanma
     */
    @NotEmpty(message = "手机号不能为空")
    @Mobile
    private String mobile;

    /**
     * 短信场景
     * 例如：1
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;

}
