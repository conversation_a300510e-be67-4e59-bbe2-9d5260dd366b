package com.qudaiji.cloud.p.module.system.service.impl;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.request.LoginLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.LoginLogDO;
import com.qudaiji.cloud.p.module.system.mapper.LoginLogMapper;
import com.qudaiji.cloud.p.module.system.service.LoginLogService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 登录日志 Service 实现
 */
@Service
@Validated
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    public PageResult<LoginLogDO> getLoginLogPage(LoginLogPageRequest request) {
        return loginLogMapper.selectPage(request);
    }

    @Override
    public void createLoginLog(LoginLogCreateReqDTO reqDTO) {
        LoginLogDO loginLog = BeanUtils.toBean(reqDTO, LoginLogDO.class);
        loginLogMapper.insert(loginLog);
    }

}
