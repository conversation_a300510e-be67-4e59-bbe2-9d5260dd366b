package com.qudaiji.cloud.p.module.infra.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.FileConfigDO;
import com.qudaiji.cloud.p.module.infra.framework.file.core.client.FileClient;
import jakarta.validation.Valid;

/**
 * 文件配置 Service 接口
 *
 * <AUTHOR>
 */
public interface FileConfigService {

    /**
     * 创建文件配置
     *
     * @param request 创建信息
     * @return 编号
     */
    Long createFileConfig(@Valid FileConfigSaveRequest request);

    /**
     * 更新文件配置
     *
     * @param request 更新信息
     */
    void updateFileConfig(@Valid FileConfigSaveRequest request);

    /**
     * 更新文件配置为 Master
     *
     * @param id 编号
     */
    void updateFileConfigMaster(Long id);

    /**
     * 删除文件配置
     *
     * @param id 编号
     */
    void deleteFileConfig(Long id);

    /**
     * 获得文件配置
     *
     * @param id 编号
     * @return 文件配置
     */
    FileConfigDO getFileConfig(Long id);

    /**
     * 获得文件配置分页
     *
     * @param request 分页查询
     * @return 文件配置分页
     */
    PageResult<FileConfigDO> getFileConfigPage(FileConfigPageRequest request);

    /**
     * 测试文件配置是否正确，通过上传文件
     *
     * @param id 编号
     * @return 文件 URL
     */
    String testFileConfig(Long id) throws Exception;

    /**
     * 获得指定编号的文件客户端
     *
     * @param id 配置编号
     * @return 文件客户端
     */
    FileClient getFileClient(Long id);

    /**
     * 获得 Master 文件客户端
     *
     * @return 文件客户端
     */
    FileClient getMasterFileClient();

}
