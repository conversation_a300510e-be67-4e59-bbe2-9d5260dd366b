package com.qudaiji.cloud.p.module.system.controller.admin.notice;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.response.NoticeResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticeSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.NoticeDO;
import com.qudaiji.cloud.p.module.system.service.NoticeService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 通知公告
 *
 * <AUTHOR>
 * @date 2025/6/12 15:13
 **/
@RestController
@RequestMapping("/system/notice")
@Validated
public class NoticeController {

    @Resource
    private NoticeService noticeService;


    /**
     * 创建通知公告
     *
     * <AUTHOR>
     * @date 2025/6/12 15:13
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:notice:create')")
    public CommonResult<Long> createNotice(@Valid @RequestBody NoticeSaveRequest request) {
        Long noticeId = noticeService.createNotice(request);
        return success(noticeId);
    }

    /**
     * 修改通知公告
     *
     * <AUTHOR>
     * @date 2025/6/12 15:13
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:notice:update')")
    public CommonResult<Boolean> updateNotice(@Valid @RequestBody NoticeSaveRequest request) {
        noticeService.updateNotice(request);
        return success(true);
    }

    /**
     * 删除通知公告
     *
     * <AUTHOR>
     * @date 2025/6/12 15:13
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:notice:delete')")
    public CommonResult<Boolean> deleteNotice(@RequestParam("id") Long id) {
        noticeService.deleteNotice(id);
        return success(true);
    }

    /**
     * 获取通知公告列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:13
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:notice:query')")
    public CommonResult<PageResult<NoticeResponse>> getNoticePage(@Validated NoticePageRequest request) {
        PageResult<NoticeDO> pageResult = noticeService.getNoticePage(request);
        return success(BeanUtils.toBean(pageResult, NoticeResponse.class));
    }


    /**
     * 获得通知公告
     *
     * <AUTHOR>
     * @date 2025/6/12 15:13
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:notice:query')")
    public CommonResult<NoticeResponse> getNotice(@RequestParam("id") Long id) {
        NoticeDO notice = noticeService.getNotice(id);
        return success(BeanUtils.toBean(notice, NoticeResponse.class));
    }
}
