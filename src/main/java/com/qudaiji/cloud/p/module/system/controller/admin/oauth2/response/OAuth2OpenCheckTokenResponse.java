package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 管理后台 - 【开放接口】校验令牌 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2OpenCheckTokenResponse {

    /**
     * 用户编号
     * 必填，例如：666
     */
    @JsonProperty("user_id")
    private Long userId;
    /**
     * 用户类型，参见 UserTypeEnum 枚举
     * 必填，例如：2
     */
    @JsonProperty("user_type")
    private Integer userType;
    /**
     * 租户编号
     * 必填，例如：1024
     */
    @JsonProperty("tenant_id")
    private Long tenantId;

    /**
     * 客户端编号
     * 必填，例如：car
     */
    @JsonProperty("client_id")
    private String clientId;
    /**
     * 授权范围
     * 必填，例如：user_info
     */
    private List<String> scopes;

    /**
     * 访问令牌
     * 必填，例如：tudou
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 过期时间，时间戳 / 1000，即单位：秒
     * 必填，例如：1593092157
     */
    private Long exp;

}
