package com.qudaiji.cloud.p.module.system.service.impl;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.SmsChannelDO;
import com.qudaiji.cloud.p.module.system.mapper.SmsChannelMapper;
import com.qudaiji.cloud.p.module.system.framework.sms.core.client.SmsClient;
import com.qudaiji.cloud.p.module.system.framework.sms.core.client.SmsClientFactory;
import com.qudaiji.cloud.p.module.system.framework.sms.core.property.SmsChannelProperties;
import com.qudaiji.cloud.p.module.system.service.SmsChannelService;
import com.qudaiji.cloud.p.module.system.service.SmsTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.p.common.constants.enums.SystemErrorCodeConstants.SMS_CHANNEL_HAS_CHILDREN;
import static com.qudaiji.cloud.p.common.constants.enums.SystemErrorCodeConstants.SMS_CHANNEL_NOT_EXISTS;

/**
 * 短信渠道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmsChannelServiceImpl implements SmsChannelService {

    @Resource
    private SmsClientFactory smsClientFactory;

    @Resource
    private SmsChannelMapper smsChannelMapper;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Override
    public Long createSmsChannel(SmsChannelSaveRequest request) {
        SmsChannelDO channel = BeanUtils.toBean(request, SmsChannelDO.class);
        smsChannelMapper.insert(channel);
        return channel.getId();
    }

    @Override
    public void updateSmsChannel(SmsChannelSaveRequest request) {
        // 校验存在
        validateSmsChannelExists(request.getId());
        // 更新
        SmsChannelDO updateObj = BeanUtils.toBean(request, SmsChannelDO.class);
        smsChannelMapper.updateById(updateObj);
    }

    @Override
    public void deleteSmsChannel(Long id) {
        // 校验存在
        validateSmsChannelExists(id);
        // 校验是否有在使用该账号的模版
        if (smsTemplateService.getSmsTemplateCountByChannelId(id) > 0) {
            throw exception(SMS_CHANNEL_HAS_CHILDREN);
        }
        // 删除
        smsChannelMapper.deleteById(id);
    }

    private SmsChannelDO validateSmsChannelExists(Long id) {
        SmsChannelDO channel = smsChannelMapper.selectById(id);
        if (channel == null) {
            throw exception(SMS_CHANNEL_NOT_EXISTS);
        }
        return channel;
    }

    @Override
    public SmsChannelDO getSmsChannel(Long id) {
        return smsChannelMapper.selectById(id);
    }

    @Override
    public List<SmsChannelDO> getSmsChannelList() {
        return smsChannelMapper.selectList();
    }

    @Override
    public PageResult<SmsChannelDO> getSmsChannelPage(SmsChannelPageRequest request) {
        return smsChannelMapper.selectPage(request);
    }

    @Override
    public SmsClient getSmsClient(Long id) {
        SmsChannelDO channel = smsChannelMapper.selectById(id);
        SmsChannelProperties properties = BeanUtils.toBean(channel, SmsChannelProperties.class);
        return smsClientFactory.createOrUpdateSmsClient(properties);
    }

    @Override
    public SmsClient getSmsClient(String code) {
        return smsClientFactory.getSmsClient(code);
    }

}
