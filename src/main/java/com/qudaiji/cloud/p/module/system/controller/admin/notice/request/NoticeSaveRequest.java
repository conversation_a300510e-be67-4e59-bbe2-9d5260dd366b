package com.qudaiji.cloud.p.module.system.controller.admin.notice.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 管理后台 - 通知公告创建/修改 Request
 */
@Data
public class NoticeSaveRequest {

    /**
     * 岗位公告编号
     * 例如：1024
     */
    private Long id;

    /**
     * 公告标题
     * 例如：小博主
     */
    @NotBlank(message = "公告标题不能为空")
    @Size(max = 50, message = "公告标题不能超过50个字符")
    private String title;

    /**
     * 公告类型
     * 例如：小博主
     */
    @NotNull(message = "公告类型不能为空")
    private Integer type;

    /**
     * 公告内容
     * 例如：半生编码
     */
    private String content;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     * 例如：1
     */
    private Integer status;

}
