package com.qudaiji.cloud.p.module.infra.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.ConfigDO;
import jakarta.validation.Valid;

/**
 * 参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigService {

    /**
     * 创建参数配置
     *
     * @param request 创建信息
     * @return 配置编号
     */
    Long createConfig(@Valid ConfigSaveRequest request);

    /**
     * 更新参数配置
     *
     * @param request 更新信息
     */
    void updateConfig(@Valid ConfigSaveRequest request);

    /**
     * 删除参数配置
     *
     * @param id 配置编号
     */
    void deleteConfig(Long id);

    /**
     * 获得参数配置
     *
     * @param id 配置编号
     * @return 参数配置
     */
    ConfigDO getConfig(Long id);

    /**
     * 根据参数键，获得参数配置
     *
     * @param key 配置键
     * @return 参数配置
     */
    ConfigDO getConfigByKey(String key);

    /**
     * 获得参数配置分页列表
     *
     * @param request 分页条件
     * @return 分页列表
     */
    PageResult<ConfigDO> getConfigPage(ConfigPageRequest request);

}
