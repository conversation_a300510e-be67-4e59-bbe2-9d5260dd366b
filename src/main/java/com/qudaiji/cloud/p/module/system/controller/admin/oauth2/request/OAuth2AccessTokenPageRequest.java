package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理后台 - 访问令牌分页
 *
 * <AUTHOR>
 * @date 2025/6/12 13:14
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class OAuth2AccessTokenPageRequest extends PageParam {

    /**
     * 用户编号
     **/
    private Long userId;

    /**
     * 用户类型，参见 UserTypeEnum 枚举
     **/
    private Integer userType;

    /**
     * 客户端编号
     **/
    private String clientId;

}
