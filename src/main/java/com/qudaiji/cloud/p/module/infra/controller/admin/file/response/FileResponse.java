package com.qudaiji.cloud.p.module.infra.controller.admin.file.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 文件 Response,不返回 content 字段，太大
 */
@Data
public class FileResponse {

    /**
     * 文件编号
     * 必填：是
     * 示例：1024
     */
    private Long id;

    /**
     * 配置编号
     * 必填：是
     * 示例：11
     */
    private Long configId;

    /**
     * 文件路径
     * 必填：是
     * 示例：jqm-p-service.jpg
     */
    private String path;

    /**
     * 原文件名
     * 必填：是
     * 示例：jqm-p-service.jpg
     */
    private String name;

    /**
     * 文件 URL
     * 必填：是
     * 示例：https://www.iocoder.cn/jqm-p-service.jpg
     */
    private String url;

    /**
     * 文件MIME类型
     * 示例：application/octet-stream
     */
    private String type;

    /**
     * 文件大小
     * 必填：是
     * 示例：2048
     */
    private Integer size;

    /**
     * 创建时间
     * 必填：是
     */
    private LocalDateTime createTime;

}
