package com.qudaiji.cloud.p.module.infra.controller.app.file;

import cn.hutool.core.io.IoUtil;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileCreateRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.response.FilePresignedUrlResponse;
import com.qudaiji.cloud.p.module.infra.controller.app.file.request.AppFileUploadRequest;
import com.qudaiji.cloud.p.module.infra.service.FileService;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 用户 App - 文件存储
 *
 * <AUTHOR>
 * @date 2025/6/12 16:05
 **/
@RestController
@RequestMapping("/infra/file")
@Validated
@Slf4j
public class AppFileController {

    @Resource
    private FileService fileService;

    /**
     * 上传文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:05
     **/
    @PostMapping("/upload")
    @PermitAll
    public CommonResult<String> uploadFile(AppFileUploadRequest appFileUploadRequest) throws Exception {
        MultipartFile file = appFileUploadRequest.getFile();
        String path = appFileUploadRequest.getPath();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    /**
     * 获取文件预签名地址
     * 模式二：前端上传文件：用于前端直接上传七牛、阿里云 OSS 等文件存储器
     *
     * <AUTHOR>
     * @date 2025/6/12 16:05
     **/
    @GetMapping("/presigned-url")
    @PermitAll
    public CommonResult<FilePresignedUrlResponse> getFilePresignedUrl(@RequestParam("path") String path) throws Exception {
        return success(fileService.getFilePresignedUrl(path));
    }

    /**
     * 创建文件
     * 模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:05
     **/
    @PostMapping("/create")
    @PermitAll
    public CommonResult<Long> createFile(@Valid @RequestBody FileCreateRequest fileCreateRequest) {
        return success(fileService.createFile(fileCreateRequest));
    }

}
