package com.qudaiji.cloud.p.module.system.controller.admin.auth.request;

import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.framework.common.validation.InEnum;
import com.qudaiji.cloud.p.common.constants.enums.social.SocialTypeEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 管理后台 - 账号密码登录 Request，如果登录并绑定社交用户，需要传递 social 开头的参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginRequest {

    /**
     * 账号
     * 示例：jqm-p-serviceyuanma
     */
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 4, max = 16, message = "账号长度为 4-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    /**
     * 密码
     * 示例：buzhidao
     */
    @NotEmpty(message = "密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password;

    // ========== 图片验证码相关 ==========

    /**
     * 验证码，验证码开启时，需要传递
     * 示例：PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==
     */
    @NotEmpty(message = "验证码不能为空", groups = CodeEnableGroup.class)
    private String captchaVerification;

    // ========== 绑定社交登录时，需要传递如下参数 ==========

    /**
     * 社交平台的类型，参见 SocialTypeEnum 枚举值
     * 示例：10
     */
    @InEnum(SocialTypeEnum.class)
    private Integer socialType;

    /**
     * 授权码
     * 示例：1024
     */
    private String socialCode;

    /**
     * state
     * 示例：9b2ffbc1-7425-4155-9894-9d5c08541d62
     */
    private String socialState;

    /**
     * 开启验证码的 Group
     */
    public interface CodeEnableGroup {}

    @AssertTrue(message = "授权码不能为空")
    public boolean isSocialCodeValid() {
        return socialType == null || StrUtil.isNotEmpty(socialCode);
    }

    @AssertTrue(message = "授权 state 不能为空")
    public boolean isSocialState() {
        return socialType == null || StrUtil.isNotEmpty(socialState);
    }

}