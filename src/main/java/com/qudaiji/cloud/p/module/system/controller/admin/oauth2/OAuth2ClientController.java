package com.qudaiji.cloud.p.module.system.controller.admin.oauth2;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request.OAuth2ClientPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response.OAuth2ClientResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request.OAuth2ClientSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.OAuth2ClientDO;
import com.qudaiji.cloud.p.module.system.service.OAuth2ClientService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - OAuth2 客户端
 *
 * <AUTHOR>
 * @date 2025/6/12 16:04
 **/
@RestController
@RequestMapping("/system/oauth2-client")
@Validated
public class OAuth2ClientController {

    @Resource
    private OAuth2ClientService oAuth2ClientService;

    /**
     * 创建 OAuth2 客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:create')")
    public CommonResult<Long> createOAuth2Client(@Valid @RequestBody OAuth2ClientSaveRequest request) {
        return success(oAuth2ClientService.createOAuth2Client(request));
    }

    /**
     * 更新 OAuth2 客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:update')")
    public CommonResult<Boolean> updateOAuth2Client(@Valid @RequestBody OAuth2ClientSaveRequest request) {
        oAuth2ClientService.updateOAuth2Client(request);
        return success(true);
    }

    /**
     * 删除 OAuth2 客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:delete')")
    public CommonResult<Boolean> deleteOAuth2Client(@RequestParam("id") Long id) {
        oAuth2ClientService.deleteOAuth2Client(id);
        return success(true);
    }

    /**
     * 获得 OAuth2 客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:query')")
    public CommonResult<OAuth2ClientResponse> getOAuth2Client(@RequestParam("id") Long id) {
        OAuth2ClientDO client = oAuth2ClientService.getOAuth2Client(id);
        return success(BeanUtils.toBean(client, OAuth2ClientResponse.class));
    }


    /**
     * 获得 OAuth2 客户端分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:oauth2-client:query')")
    public CommonResult<PageResult<OAuth2ClientResponse>> getOAuth2ClientPage(@Valid OAuth2ClientPageRequest request) {
        PageResult<OAuth2ClientDO> pageResult = oAuth2ClientService.getOAuth2ClientPage(request);
        return success(BeanUtils.toBean(pageResult, OAuth2ClientResponse.class));
    }

}
