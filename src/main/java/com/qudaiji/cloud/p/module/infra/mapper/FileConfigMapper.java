package com.qudaiji.cloud.p.module.infra.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.entity.FileConfigDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface FileConfigMapper extends BaseMapperX<FileConfigDO> {

    default PageResult<FileConfigDO> selectPage(FileConfigPageRequest fileConfigPageRequest) {
        return selectPage(fileConfigPageRequest, new LambdaQueryWrapperX<FileConfigDO>()
                .likeIfPresent(FileConfigDO::getName, fileConfigPageRequest.getName())
                .eqIfPresent(FileConfigDO::getStorage, fileConfigPageRequest.getStorage())
                .betweenIfPresent(FileConfigDO::getCreateTime, fileConfigPageRequest.getCreateTime())
                .orderByDesc(FileConfigDO::getId));
    }

    default FileConfigDO selectByMaster() {
        return selectOne(FileConfigDO::getMaster, true);
    }

}
