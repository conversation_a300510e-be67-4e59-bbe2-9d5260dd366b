package com.qudaiji.cloud.p.module.system.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.request.LoginLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.LoginLogDO;
import jakarta.validation.Valid;

/**
 * 登录日志 Service 接口
 */
public interface LoginLogService {

    /**
     * 获得登录日志分页
     *
     * @param request 分页条件
     * @return 登录日志分页
     */
    PageResult<LoginLogDO> getLoginLogPage(LoginLogPageRequest request);

    /**
     * 创建登录日志
     *
     * @param reqDTO 日志信息
     */
    void createLoginLog(@Valid LoginLogCreateReqDTO reqDTO);

}
