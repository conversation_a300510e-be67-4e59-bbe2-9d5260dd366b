package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.MailLogDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MailLogMapper extends BaseMapperX<MailLogDO> {

    default PageResult<MailLogDO> selectPage(MailLogPageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<MailLogDO>()
                .eqIfPresent(MailLogDO::getUserId, request.getUserId())
                .eqIfPresent(MailLogDO::getUserType, request.getUserType())
                .likeIfPresent(MailLogDO::getToMail, request.getToMail())
                .eqIfPresent(MailLogDO::getAccountId, request.getAccountId())
                .eqIfPresent(MailLogDO::getTemplateId, request.getTemplateId())
                .eqIfPresent(MailLogDO::getSendStatus, request.getSendStatus())
                .betweenIfPresent(MailLogDO::getSendTime, request.getSendTime())
                .orderByDesc(MailLogDO::getId));
    }

}
