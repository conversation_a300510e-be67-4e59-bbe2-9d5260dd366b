package com.qudaiji.cloud.p.module.infra.convert.redis;

import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.p.module.infra.controller.admin.redis.response.RedisMonitorResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Properties;

@Mapper
public interface RedisConvert {

    RedisConvert INSTANCE = Mappers.getMapper(RedisConvert.class);

    default RedisMonitorResponse build(Properties info, Long dbSize, Properties commandStats) {
        RedisMonitorResponse response = RedisMonitorResponse.builder().info(info).dbSize(dbSize)
                .commandStats(new ArrayList<>(commandStats.size())).build();
        commandStats.forEach((key, value) -> {
            response.getCommandStats().add(RedisMonitorResponse.CommandStat.builder()
                    .command(StrUtil.subAfter((String) key, "cmdstat_", false))
                    .calls(Long.valueOf(StrUtil.subBetween((String) value, "calls=", ",")))
                    .usec(Long.valueOf(StrUtil.subBetween((String) value, "usec=", ",")))
                    .build());
        });
        return response;
    }

}
