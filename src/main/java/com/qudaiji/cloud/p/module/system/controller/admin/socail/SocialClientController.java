package com.qudaiji.cloud.p.module.system.controller.admin.socail;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.api.social.SocialClientApi;
import com.qudaiji.cloud.p.module.system.api.social.dto.SocialWxaSubscribeMessageSendReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialClientPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.response.SocialClientResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialClientSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.SocialClientDO;
import com.qudaiji.cloud.p.module.system.service.SocialClientService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 社交客户端
 *
 * <AUTHOR>
 * @date 2025/6/12 15:33
 **/
@RestController
@RequestMapping("/system/social-client")
@Validated
public class SocialClientController {

    @Resource
    private SocialClientService socialClientService;
    @Resource
    private SocialClientApi socialClientApi;

    /**
     * 创建社交客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 15:33
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:social-client:create')")
    public CommonResult<Long> createSocialClient(@Valid @RequestBody SocialClientSaveRequest request) {
        return success(socialClientService.createSocialClient(request));
    }

    /**
     * 更新社交客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 15:34
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:social-client:update')")
    public CommonResult<Boolean> updateSocialClient(@Valid @RequestBody SocialClientSaveRequest request) {
        socialClientService.updateSocialClient(request);
        return success(true);
    }

    /**
     * 删除社交客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 15:34
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:social-client:delete')")
    public CommonResult<Boolean> deleteSocialClient(@RequestParam("id") Long id) {
        socialClientService.deleteSocialClient(id);
        return success(true);
    }

    /**
     * 获得社交客户端
     *
     * <AUTHOR>
     * @date 2025/6/12 15:34
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:social-client:query')")
    public CommonResult<SocialClientResponse> getSocialClient(@RequestParam("id") Long id) {
        SocialClientDO client = socialClientService.getSocialClient(id);
        return success(BeanUtils.toBean(client, SocialClientResponse.class));
    }

    /**
     * 获得社交客户端分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:34
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:social-client:query')")
    public CommonResult<PageResult<SocialClientResponse>> getSocialClientPage(@Valid SocialClientPageRequest request) {
        PageResult<SocialClientDO> pageResult = socialClientService.getSocialClientPage(request);
        return success(BeanUtils.toBean(pageResult, SocialClientResponse.class));
    }


    /**
     * 发送订阅消息
     * 用于测试
     *
     * <AUTHOR>
     * @date 2025/6/12 15:34
     **/
    @PostMapping("/send-subscribe-message")
    @PreAuthorize("@ss.hasPermission('system:social-client:query')")
    public void sendSubscribeMessage(@RequestBody SocialWxaSubscribeMessageSendReqDTO reqDTO) {
        socialClientApi.sendWxaSubscribeMessage(reqDTO);
    }

}
