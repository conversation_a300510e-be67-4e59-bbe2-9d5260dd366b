package com.qudaiji.cloud.p.module.infra.controller.admin.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileCreateRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FilePageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileUploadRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.response.FilePresignedUrlResponse;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.response.FileResponse;
import com.qudaiji.cloud.p.module.infra.entity.FileDO;
import com.qudaiji.cloud.p.module.infra.service.FileService;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.module.infra.framework.file.core.utils.FileTypeUtils.writeAttachment;

/**
 * 管理后台 - 文件存储
 *
 * <AUTHOR>
 * @date 2025/6/12 16:22
 **/
@RestController
@RequestMapping("/infra/file")
@Validated
@Slf4j
public class FileController {

    @Resource
    private FileService fileService;

    /**
     * 上传文件
     * 模式一：后端上传文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:22
     **/
    @PostMapping("/upload")
    public CommonResult<String> uploadFile(FileUploadRequest fileUploadRequest) throws Exception {
        MultipartFile file = fileUploadRequest.getFile();
        String path = fileUploadRequest.getPath();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    /**
     * 上传文件
     * 模式二：前端上传文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:22
     **/
    @GetMapping("/presigned-url")
    public CommonResult<FilePresignedUrlResponse> getFilePresignedUrl(@RequestParam("path") String path) throws Exception {
        return success(fileService.getFilePresignedUrl(path));
    }

    /**
     * 创建文件
     * 模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:23
     **/
    @PostMapping("/create")
    public CommonResult<Long> createFile(@Valid @RequestBody FileCreateRequest fileCreateRequest) {
        return success(fileService.createFile(fileCreateRequest));
    }

    /**
     * 删除文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:23
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('infra:file:delete')")
    public CommonResult<Boolean> deleteFile(@RequestParam("id") Long id) throws Exception {
        fileService.deleteFile(id);
        return success(true);
    }

    /**
     * 下载文件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:23
     **/
    @GetMapping("/{configId}/get/**")
    @PermitAll
    public void getFileContent(HttpServletRequest request,
                               HttpServletResponse response,
                               @PathVariable("configId") Long configId) throws Exception {
        // 获取请求的路径
        String path = StrUtil.subAfter(request.getRequestURI(), "/get/", false);
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        // 解码，解决中文路径的问题 https://gitee.com/zhijiantianya/ruoyi-vue-pro/pulls/807/
        path = URLUtil.decode(path);

        // 读取内容
        byte[] content = fileService.getFileContent(configId, path);
        if (content == null) {
            log.warn("[getFileContent][configId({}) path({}) 文件不存在]", configId, path);
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }
        writeAttachment(response, path, content);
    }

    /**
     * 获得文件分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:23
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<PageResult<FileResponse>> getFilePage(@Valid FilePageRequest pageVO) {
        PageResult<FileDO> pageResult = fileService.getFilePage(pageVO);
        return success(BeanUtils.toBean(pageResult, FileResponse.class));
    }

}
