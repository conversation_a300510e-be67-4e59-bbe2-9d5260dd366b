package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 管理后台 - OAuth2 更新用户基本信息 Request
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2UserUpdateRequest {

    /**
     * 用户昵称
     * 必填，例如：芋艿
     */
    @Size(max = 30, message = "用户昵称长度不能超过 30 个字符")
    private String nickname;

    /**
     * 用户邮箱
     * 例如：<EMAIL>
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    /**
     * 手机号码
     * 例如：15601691300
     */
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    private String mobile;

    /**
     * 用户性别，参见 SexEnum 枚举类
     * 例如：1
     */
    private Integer sex;

}
