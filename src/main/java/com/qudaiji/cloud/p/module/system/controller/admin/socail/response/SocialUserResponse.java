package com.qudaiji.cloud.p.module.system.controller.admin.socail.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 社交用户 Response
 */
@Data
public class SocialUserResponse {

    /**
     * 主键(自增策略)
     * 例如：14569
     */
    private Long id;

    /**
     * 社交平台的类型
     * 例如：30
     */
    private Integer type;

    /**
     * 社交 openid
     * 例如：666
     */
    private String openid;

    /**
     * 社交 token
     * 例如：666
     */
    private String token;

    /**
     * 原始 Token 数据，一般是 JSON 格式
     * 例如：{}
     */
    private String rawTokenInfo;

    /**
     * 用户昵称
     * 例如：芋艿
     */
    private String nickname;

    /**
     * 用户头像
     * 例如：https://www.iocoder.cn/xxx.png
     */
    private String avatar;

    /**
     * 原始用户数据，一般是 JSON 格式
     * 例如：{}
     */
    private String rawUserInfo;

    /**
     * 最后一次的认证 code
     * 例如：666666
     */
    private String code;

    /**
     * 最后一次的认证 state
     * 例如：123456
     */
    private String state;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
