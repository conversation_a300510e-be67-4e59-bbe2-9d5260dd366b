package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 管理后台 - 访问令牌 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2AccessTokenResponse {

    /**
     * 编号
     * 必填，例如：1024
     */
    private Long id;

    /**
     * 访问令牌
     * 必填，例如：tudou
     */
    private String accessToken;

    /**
     * 刷新令牌
     * 必填，例如：nice
     */
    private String refreshToken;

    /**
     * 用户编号
     * 必填，例如：666
     */
    private Long userId;

    /**
     * 用户类型，参见 UserTypeEnum 枚举
     * 必填，例如：2
     */
    private Integer userType;

    /**
     * 客户端编号
     * 必填，例如：2
     */
    private String clientId;

    /**
     * 创建时间
     * 必填
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     * 必填
     */
    private LocalDateTime expiresTime;

}
