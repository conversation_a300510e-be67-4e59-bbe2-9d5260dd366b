package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - OAuth2 客户端 Response
 */
@Data
public class OAuth2ClientResponse {

    /**
     * 编号
     * 必填，例如：1024
     */
    private Long id;

    /**
     * 客户端编号
     * 必填，例如：tudou
     */
    private String clientId;

    /**
     * 客户端密钥
     * 必填，例如：fan
     */
    private String secret;

    /**
     * 应用名
     * 必填，例如：土豆
     */
    private String name;

    /**
     * 应用图标
     * 必填，例如：https://www.iocoder.cn/xx.png
     */
    private String logo;

    /**
     * 应用描述
     * 例如：我是一个应用
     */
    private String description;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 必填，例如：1
     */
    private Integer status;

    /**
     * 访问令牌的有效期
     * 必填，例如：8640
     */
    private Integer accessTokenValiditySeconds;

    /**
     * 刷新令牌的有效期
     * 必填，例如：8640000
     */
    private Integer refreshTokenValiditySeconds;

    /**
     * 可重定向的 URI 地址
     * 必填，例如：https://www.iocoder.cn
     */
    private List<String> redirectUris;

    /**
     * 授权类型，参见 OAuth2GrantTypeEnum 枚举
     * 必填，例如：password
     */
    private List<String> authorizedGrantTypes;

    /**
     * 授权范围
     * 例如：user_info
     */
    private List<String> scopes;

    /**
     * 自动通过的授权范围
     * 例如：user_info
     */
    private List<String> autoApproveScopes;

    /**
     * 权限
     * 例如：system:user:query
     */
    private List<String> authorities;

    /**
     * 资源
     * 例如：1024
     */
    private List<String> resourceIds;

    /**
     * 附加信息
     * 例如：{yunai: true}
     */
    private String additionalInformation;

    /**
     * 创建时间
     * 必填
     */
    private LocalDateTime createTime;

}
