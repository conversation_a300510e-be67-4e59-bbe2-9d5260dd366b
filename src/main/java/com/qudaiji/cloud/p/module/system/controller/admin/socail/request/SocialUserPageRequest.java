package com.qudaiji.cloud.p.module.system.controller.admin.socail.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.qudaiji.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 社交用户分页 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialUserPageRequest extends PageParam {

    /**
     * 社交平台的类型
     * 例如：30
     */
    private Integer type;

    /**
     * 用户昵称
     * 例如：李四
     */
    private String nickname;

    /**
     * 社交 openid
     * 例如：oz-Jdt0kd_jdhUxJHQdBJMlOFN7w
     */
    private String openid;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
