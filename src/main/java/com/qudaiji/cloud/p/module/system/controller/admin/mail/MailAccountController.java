package com.qudaiji.cloud.p.module.system.controller.admin.mail;


import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailAccountPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.response.MailAccountResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailAccountSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.response.MailAccountSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.MailAccountDO;
import com.qudaiji.cloud.p.module.system.service.MailAccountService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 邮箱账号
 *
 * <AUTHOR>
 * @date 2025/6/12 15:34
 **/
@RestController
@RequestMapping("/system/mail-account")
public class MailAccountController {

    @Resource
    private MailAccountService mailAccountService;

    /**
     * 创建邮箱账号
     *
     * <AUTHOR>
     * @date 2025/6/12 15:35
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:mail-account:create')")
    public CommonResult<Long> createMailAccount(@Valid @RequestBody MailAccountSaveRequest request) {
        return success(mailAccountService.createMailAccount(request));
    }

    /**
     * 修改邮箱账号
     *
     * <AUTHOR>
     * @date 2025/6/12 15:35
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:mail-account:update')")
    public CommonResult<Boolean> updateMailAccount(@Valid @RequestBody MailAccountSaveRequest request) {
        mailAccountService.updateMailAccount(request);
        return success(true);
    }

    /**
     * 删除邮箱账号
     *
     * <AUTHOR>
     * @date 2025/6/12 15:35
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:mail-account:delete')")
    public CommonResult<Boolean> deleteMailAccount(@RequestParam Long id) {
        mailAccountService.deleteMailAccount(id);
        return success(true);
    }

    /**
     * 获得邮箱账号
     *
     * <AUTHOR>
     * @date 2025/6/12 15:35
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:mail-account:query')")
    public CommonResult<MailAccountResponse> getMailAccount(@RequestParam("id") Long id) {
        MailAccountDO account = mailAccountService.getMailAccount(id);
        return success(BeanUtils.toBean(account, MailAccountResponse.class));
    }


    /**
     * 获得邮箱账号分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:37
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:mail-account:query')")
    public CommonResult<PageResult<MailAccountResponse>> getMailAccountPage(@Valid MailAccountPageRequest request) {
        PageResult<MailAccountDO> pageResult = mailAccountService.getMailAccountPage(request);
        return success(BeanUtils.toBean(pageResult, MailAccountResponse.class));
    }

    /**
     * 获得邮箱账号精简列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:37
     **/
    @GetMapping({"/list-all-simple", "simple-list"})
    public CommonResult<List<MailAccountSimpleResponse>> getSimpleMailAccountList() {
        List<MailAccountDO> list = mailAccountService.getMailAccountList();
        return success(BeanUtils.toBean(list, MailAccountSimpleResponse.class));
    }

}
