package com.qudaiji.cloud.p.module.system.controller.admin.user.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 管理后台 - 用户个人中心更新密码 Request
 *
 * <AUTHOR>
 * @date 2025/6/12 15:15
 **/
@Data
public class UserProfileUpdatePasswordRequest {

    /**
     * 旧密码
     **/
    @NotEmpty(message = "旧密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String oldPassword;

    /**
     * 新密码
     **/
    @NotEmpty(message = "新密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String newPassword;

}
