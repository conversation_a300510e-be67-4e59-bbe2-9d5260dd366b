package com.qudaiji.cloud.p.module.system.service.impl;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.api.logger.dto.OperateLogCreateReqDTO;
import com.qudaiji.cloud.p.module.system.api.logger.dto.OperateLogPageReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.request.OperateLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.OperateLogDO;
import com.qudaiji.cloud.p.module.system.mapper.OperateLogMapper;
import com.qudaiji.cloud.p.module.system.service.OperateLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OperateLogServiceImpl implements OperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;

    @Override
    public void createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        OperateLogDO log = BeanUtils.toBean(createReqDTO, OperateLogDO.class);
        operateLogMapper.insert(log);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageRequest request) {
        return operateLogMapper.selectPage(request);
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        return operateLogMapper.selectPage(pageReqDTO);
    }

}
