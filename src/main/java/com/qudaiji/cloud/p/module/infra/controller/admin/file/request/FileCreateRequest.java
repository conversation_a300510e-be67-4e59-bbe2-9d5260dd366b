package com.qudaiji.cloud.p.module.infra.controller.admin.file.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 管理后台 - 文件创建 Request
 */
@Data
public class FileCreateRequest {

    /**
     * 文件配置编号
     * 必填：是
     * 示例：11
     */
    @NotNull(message = "文件配置编号不能为空")
    private Long configId;

    /**
     * 文件路径
     * 必填：是
     * 示例：jqm-p-service.jpg
     */
    @NotNull(message = "文件路径不能为空")
    private String path;

    /**
     * 原文件名
     * 必填：是
     * 示例：jqm-p-service.jpg
     */
    @NotNull(message = "原文件名不能为空")
    private String name;

    /**
     * 文件 URL
     * 必填：是
     * 示例：https://www.iocoder.cn/jqm-p-service.jpg
     */
    @NotNull(message = "文件 URL不能为空")
    private String url;

    /**
     * 文件 MIME 类型
     * 示例：application/octet-stream
     */
    private String type;

    /**
     * 文件大小
     * 必填：是
     * 示例：2048
     */
    private Integer size;

}
