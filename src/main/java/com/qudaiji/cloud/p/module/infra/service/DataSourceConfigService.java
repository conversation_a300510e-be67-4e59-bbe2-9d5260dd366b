package com.qudaiji.cloud.p.module.infra.service;

import com.qudaiji.cloud.p.module.infra.controller.admin.db.request.DataSourceConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.DataSourceConfigDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 数据源配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DataSourceConfigService {

    /**
     * 创建数据源配置
     *
     * @param request 创建信息
     * @return 编号
     */
    Long createDataSourceConfig(@Valid DataSourceConfigSaveRequest request);

    /**
     * 更新数据源配置
     *
     * @param request 更新信息
     */
    void updateDataSourceConfig(@Valid DataSourceConfigSaveRequest request);

    /**
     * 删除数据源配置
     *
     * @param id 编号
     */
    void deleteDataSourceConfig(Long id);

    /**
     * 获得数据源配置
     *
     * @param id 编号
     * @return 数据源配置
     */
    DataSourceConfigDO getDataSourceConfig(Long id);

    /**
     * 获得数据源配置列表
     *
     * @return 数据源配置列表
     */
    List<DataSourceConfigDO> getDataSourceConfigList();

}
