package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialClientPageRequest;
import com.qudaiji.cloud.p.module.system.entity.SocialClientDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SocialClientMapper extends BaseMapperX<SocialClientDO> {

    default SocialClientDO selectBySocialTypeAndUserType(Integer socialType, Integer userType) {
        return selectOne(SocialClientDO::getSocialType, socialType,
                SocialClientDO::getUserType, userType);
    }

    default PageResult<SocialClientDO> selectPage(SocialClientPageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<SocialClientDO>()
                .likeIfPresent(SocialClientDO::getName, request.getName())
                .eqIfPresent(SocialClientDO::getSocialType, request.getSocialType())
                .eqIfPresent(SocialClientDO::getUserType, request.getUserType())
                .likeIfPresent(SocialClientDO::getClientId, request.getClientId())
                .eqIfPresent(SocialClientDO::getStatus, request.getStatus())
                .orderByDesc(SocialClientDO::getId));
    }

}
