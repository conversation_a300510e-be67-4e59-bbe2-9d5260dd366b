package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.*;

/**
 * 管理后台 - OAuth2 客户端分页 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OAuth2ClientPageRequest extends PageParam {

    /**
     * 应用名，模糊匹配
     * 例如：土豆
     */
    private String name;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 例如：1
     */
    private Integer status;

}
