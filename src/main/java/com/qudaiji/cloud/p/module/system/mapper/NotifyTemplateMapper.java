package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplatePageRequest;
import com.qudaiji.cloud.p.module.system.entity.NotifyTemplateDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface NotifyTemplateMapper extends BaseMapperX<NotifyTemplateDO> {

    default NotifyTemplateDO selectByCode(String code) {
        return selectOne(NotifyTemplateDO::getCode, code);
    }

    default PageResult<NotifyTemplateDO> selectPage(NotifyTemplatePageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<NotifyTemplateDO>()
                .likeIfPresent(NotifyTemplateDO::getCode, request.getCode())
                .likeIfPresent(NotifyTemplateDO::getName, request.getName())
                .eqIfPresent(NotifyTemplateDO::getStatus, request.getStatus())
                .betweenIfPresent(NotifyTemplateDO::getCreateTime, request.getCreateTime())
                .orderByDesc(NotifyTemplateDO::getId));
    }

}
