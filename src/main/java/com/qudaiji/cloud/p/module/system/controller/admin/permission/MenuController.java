package com.qudaiji.cloud.p.module.system.controller.admin.permission;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.MenuListRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.response.MenuResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.MenuSaveVO;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.response.MenuSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.MenuDO;
import com.qudaiji.cloud.p.module.system.service.MenuService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 菜单
 *
 * <AUTHOR>
 * @date 2025/6/12 16:32
 **/
@RestController
@RequestMapping("/system/menu")
@Validated
public class MenuController {

    @Resource
    private MenuService menuService;

    /**
     * 创建菜单
     *
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:menu:create')")
    public CommonResult<Long> createMenu(@Valid @RequestBody MenuSaveVO menuSaveVO) {
        Long menuId = menuService.createMenu(menuSaveVO);
        return success(menuId);
    }

    /**
     * 修改菜单
     *
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:menu:update')")
    public CommonResult<Boolean> updateMenu(@Valid @RequestBody MenuSaveVO menuSaveVO) {
        menuService.updateMenu(menuSaveVO);
        return success(true);
    }

    /**
     * 删除菜单
     *
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:menu:delete')")
    public CommonResult<Boolean> deleteMenu(@RequestParam("id") Long id) {
        menuService.deleteMenu(id);
        return success(true);
    }

    /**
     * 获取菜单列表
     * 用于【菜单管理】界面
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('system:menu:query')")
    public CommonResult<List<MenuResponse>> getMenuList(MenuListRequest request) {
        List<MenuDO> list = menuService.getMenuList(request);
        list.sort(Comparator.comparing(MenuDO::getSort));
        return success(BeanUtils.toBean(list, MenuResponse.class));
    }

    /**
     * 获取菜单精简信息列表
     * 只包含被开启的菜单，用于【角色分配菜单】功能的选项。在多租户的场景下，会只返回租户所在套餐有的菜单
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @GetMapping({"/list-all-simple", "simple-list"})
    public CommonResult<List<MenuSimpleResponse>> getSimpleMenuList() {
        List<MenuDO> list = menuService.getMenuListByTenant(
                new MenuListRequest().setStatus(CommonStatusEnum.ENABLE.getStatus()));
        list = menuService.filterDisableMenus(list);
        list.sort(Comparator.comparing(MenuDO::getSort));
        return success(BeanUtils.toBean(list, MenuSimpleResponse.class));
    }


    /**
     * 获取菜单信息
     *
     * <AUTHOR>
     * @date 2025/6/12 16:32
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:menu:query')")
    public CommonResult<MenuResponse> getMenu(Long id) {
        MenuDO menu = menuService.getMenu(id);
        return success(BeanUtils.toBean(menu, MenuResponse.class));
    }

}
