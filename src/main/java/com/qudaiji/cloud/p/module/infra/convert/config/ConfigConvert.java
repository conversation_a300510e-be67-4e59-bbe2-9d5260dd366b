package com.qudaiji.cloud.p.module.infra.convert.config;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigResponse;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.ConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ConfigConvert {

    ConfigConvert INSTANCE = Mappers.getMapper(ConfigConvert.class);

    PageResult<ConfigResponse> convertPage(PageResult<ConfigDO> page);

    List<ConfigResponse> convertList(List<ConfigDO> list);

    @Mapping(source = "configKey", target = "key")
    ConfigResponse convert(ConfigDO bean);

    @Mapping(source = "key", target = "configKey")
    ConfigDO convert(ConfigSaveRequest bean);

}
