package com.qudaiji.cloud.p.module.infra.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.entity.ConfigDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ConfigMapper extends BaseMapperX<ConfigDO> {

    default ConfigDO selectByKey(String key) {
        return selectOne(ConfigDO::getConfigKey, key);
    }

    default PageResult<ConfigDO> selectPage(ConfigPageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<ConfigDO>()
                .likeIfPresent(ConfigDO::getName, request.getName())
                .likeIfPresent(ConfigDO::getConfigKey, request.getKey())
                .eqIfPresent(ConfigDO::getType, request.getType())
                .betweenIfPresent(ConfigDO::getCreateTime, request.getCreateTime()));
    }

}
