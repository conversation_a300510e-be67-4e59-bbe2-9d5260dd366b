package com.qudaiji.cloud.p.module.system.controller.admin.notify.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 管理后台 - 站内信 Response
 */
@Data
public class NotifyMessageResponse {

    /**
     * ID
     * 例如：1024
     */
    private Long id;

    /**
     * 用户编号
     * 例如：25025
     */
    private Long userId;

    /**
     * 用户类型，参见 UserTypeEnum 枚举
     * 例如：1
     */
    private Byte userType;

    /**
     * 模版编号
     * 例如：13013
     */
    private Long templateId;

    /**
     * 模板编码
     * 例如：test_01
     */
    private String templateCode;

    /**
     * 模版发送人名称
     * 例如：芋艿
     */
    private String templateNickname;

    /**
     * 模版内容
     * 例如：测试内容
     */
    private String templateContent;

    /**
     * 模版类型
     * 例如：2
     */
    private Integer templateType;

    /**
     * 模版参数
     */
    private Map<String, Object> templateParams;

    /**
     * 是否已读
     * 例如：true
     */
    private Boolean readStatus;

    /**
     * 阅读时间
     */
    private LocalDateTime readTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
