package com.qudaiji.cloud.p.module.infra.controller.admin.file.response;

import com.qudaiji.cloud.p.module.infra.framework.file.core.client.FileClientConfig;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 文件配置 Response
 */
@Data
public class FileConfigResponse {

    /**
     * 编号
     * 必填：是
     * 示例：1
     */
    private Long id;

    /**
     * 配置名
     * 必填：是
     * 示例：S3 - 阿里云
     */
    private String name;

    /**
     * 存储器，参见 FileStorageEnum 枚举类
     * 必填：是
     * 示例：1
     */
    private Integer storage;

    /**
     * 是否为主配置
     * 必填：是
     * 示例：true
     */
    private Boolean master;

    /**
     * 存储配置
     * 必填：是
     */
    private FileClientConfig config;

    /**
     * 备注
     * 示例：我是备注
     */
    private String remark;

    /**
     * 创建时间
     * 必填：是
     */
    private LocalDateTime createTime;

}
