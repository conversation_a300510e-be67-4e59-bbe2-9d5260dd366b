package com.qudaiji.cloud.p.module.infra.controller.admin.file.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 管理后台 - 文件配置创建/修改 Request
 */
@Data
public class FileConfigSaveRequest {

    /**
     * 编号
     * 示例：1
     */
    private Long id;

    /**
     * 配置名
     * 必填：是
     * 示例：S3 - 阿里云
     */
    @NotNull(message = "配置名不能为空")
    private String name;

    /**
     * 存储器，参见 FileStorageEnum 枚举类
     * 必填：是
     * 示例：1
     */
    @NotNull(message = "存储器不能为空")
    private Integer storage;

    /**
     * 存储配置,配置是动态参数，所以使用 Map 接收
     * 必填：是
     */
    @NotNull(message = "存储配置不能为空")
    private Map<String, Object> config;

    /**
     * 备注
     * 示例：我是备注
     */
    private String remark;

}
