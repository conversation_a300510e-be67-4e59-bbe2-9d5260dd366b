package com.qudaiji.cloud.p.module.system.controller.admin.notify.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.qudaiji.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 站内信分页 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotifyMessagePageRequest extends PageParam {

    /**
     * 用户编号
     * 例如：25025
     */
    private Long userId;

    /**
     * 用户类型
     * 例如：1
     */
    private Integer userType;

    /**
     * 模板编码
     * 例如：test_01
     */
    private String templateCode;

    /**
     * 模版类型
     * 例如：2
     */
    private Integer templateType;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
