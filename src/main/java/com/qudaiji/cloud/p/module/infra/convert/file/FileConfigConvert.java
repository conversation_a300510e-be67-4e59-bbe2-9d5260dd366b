package com.qudaiji.cloud.p.module.infra.convert.file;

import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.FileConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 文件配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface FileConfigConvert {

    FileConfigConvert INSTANCE = Mappers.getMapper(FileConfigConvert.class);

    @Mapping(target = "config", ignore = true)
    FileConfigDO convert(FileConfigSaveRequest bean);

}
