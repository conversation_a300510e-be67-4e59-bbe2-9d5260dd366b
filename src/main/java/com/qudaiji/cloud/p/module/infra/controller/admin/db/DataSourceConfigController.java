package com.qudaiji.cloud.p.module.infra.controller.admin.db;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.infra.controller.admin.db.response.DataSourceConfigResponse;
import com.qudaiji.cloud.p.module.infra.controller.admin.db.request.DataSourceConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.DataSourceConfigDO;
import com.qudaiji.cloud.p.module.infra.service.DataSourceConfigService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 数据源配置
 *
 * <AUTHOR>
 * @date 2025/6/12 15:28
 **/
@RestController
@RequestMapping("/infra/data-source-config")
@Validated
public class DataSourceConfigController {

    @Resource
    private DataSourceConfigService dataSourceConfigService;

    /**
     * 创建数据源配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:28
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('infra:data-source-config:create')")
    public CommonResult<Long> createDataSourceConfig(@Valid @RequestBody DataSourceConfigSaveRequest request) {
        return success(dataSourceConfigService.createDataSourceConfig(request));
    }

    /**
     * 更新数据源配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:28
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('infra:data-source-config:update')")
    public CommonResult<Boolean> updateDataSourceConfig(@Valid @RequestBody DataSourceConfigSaveRequest request) {
        dataSourceConfigService.updateDataSourceConfig(request);
        return success(true);
    }

    /**
     * 删除数据源配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:28
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('infra:data-source-config:delete')")
    public CommonResult<Boolean> deleteDataSourceConfig(@RequestParam("id") Long id) {
        dataSourceConfigService.deleteDataSourceConfig(id);
        return success(true);
    }

    /**
     * 获得数据源配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:28
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('infra:data-source-config:query')")
    public CommonResult<DataSourceConfigResponse> getDataSourceConfig(@RequestParam("id") Long id) {
        DataSourceConfigDO config = dataSourceConfigService.getDataSourceConfig(id);
        return success(BeanUtils.toBean(config, DataSourceConfigResponse.class));
    }

    /**
     * 获得数据源配置列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:28
     **/
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('infra:data-source-config:query')")
    public CommonResult<List<DataSourceConfigResponse>> getDataSourceConfigList() {
        List<DataSourceConfigDO> list = dataSourceConfigService.getDataSourceConfigList();
        return success(BeanUtils.toBean(list, DataSourceConfigResponse.class));
    }

}
