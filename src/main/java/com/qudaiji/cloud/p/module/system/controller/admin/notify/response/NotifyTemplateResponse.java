package com.qudaiji.cloud.p.module.system.controller.admin.notify.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 站内信模版 Response
 */
@Data
public class NotifyTemplateResponse {

    /**
     * ID
     * 例如：1024
     */
    private Long id;

    /**
     * 模版名称
     * 例如：测试模版
     */
    private String name;

    /**
     * 模版编码
     * 例如：SEND_TEST
     */
    private String code;

    /**
     * 模版类型，对应 system_notify_template_type 字典
     * 例如：1
     */
    private Integer type;

    /**
     * 发送人名称
     * 例如：土豆
     */
    private String nickname;

    /**
     * 模版内容
     * 例如：我是模版内容
     */
    private String content;

    /**
     * 参数数组
     * 例如：name,code
     */
    private List<String> params;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 例如：1
     */
    private Integer status;

    /**
     * 备注
     * 例如：我是备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
