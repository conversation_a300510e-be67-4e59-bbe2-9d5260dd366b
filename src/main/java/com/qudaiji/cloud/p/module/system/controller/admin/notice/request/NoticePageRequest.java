package com.qudaiji.cloud.p.module.system.controller.admin.notice.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理后台 - 通知公告分页 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticePageRequest extends PageParam {

    /**
     * 通知公告名称，模糊匹配
     */
    private String title;

    /**
     * 展示状态，参见 CommonStatusEnum 枚举类
     */
    private Integer status;

}
