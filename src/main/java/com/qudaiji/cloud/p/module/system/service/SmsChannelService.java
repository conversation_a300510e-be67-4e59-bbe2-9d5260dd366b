package com.qudaiji.cloud.p.module.system.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.SmsChannelDO;
import com.qudaiji.cloud.p.module.system.framework.sms.core.client.SmsClient;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 短信渠道 Service 接口
 *
 * <AUTHOR>
 * @since 2021/1/25 9:24
 */
public interface SmsChannelService {

    /**
     * 创建短信渠道
     *
     * @param request 创建信息
     * @return 编号
     */
    Long createSmsChannel(@Valid SmsChannelSaveRequest request);

    /**
     * 更新短信渠道
     *
     * @param request 更新信息
     */
    void updateSmsChannel(@Valid SmsChannelSaveRequest request);

    /**
     * 删除短信渠道
     *
     * @param id 编号
     */
    void deleteSmsChannel(Long id);

    /**
     * 获得短信渠道
     *
     * @param id 编号
     * @return 短信渠道
     */
    SmsChannelDO getSmsChannel(Long id);

    /**
     * 获得所有短信渠道列表
     *
     * @return 短信渠道列表
     */
    List<SmsChannelDO> getSmsChannelList();

    /**
     * 获得短信渠道分页
     *
     * @param request 分页查询
     * @return 短信渠道分页
     */
    PageResult<SmsChannelDO> getSmsChannelPage(SmsChannelPageRequest request);

    /**
     * 获得短信客户端
     *
     * @param id 编号
     * @return 短信客户端
     */
    SmsClient getSmsClient(Long id);

    /**
     * 获得短信客户端
     *
     * @param code 编码
     * @return 短信客户端
     */
    SmsClient getSmsClient(String code);

}
