package com.qudaiji.cloud.p.module.system.controller.admin.oauth2;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.constants.enums.logger.LoginLogTypeEnum;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request.OAuth2AccessTokenPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response.OAuth2AccessTokenResponse;
import com.qudaiji.cloud.p.module.system.entity.OAuth2AccessTokenDO;
import com.qudaiji.cloud.p.module.system.service.AdminAuthService;
import com.qudaiji.cloud.p.module.system.service.OAuth2TokenService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - OAuth2.0 令牌
 *
 * <AUTHOR>
 * @date 2025/6/12 15:29
 **/
@RestController
@RequestMapping("/system/oauth2-token")
public class OAuth2TokenController {

    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private AdminAuthService authService;

    /**
     * 获得访问令牌分页，只返回有效期内的
     *
     * <AUTHOR>
     * @date 2025/6/12 15:29
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:oauth2-token:page')")
    public CommonResult<PageResult<OAuth2AccessTokenResponse>> getAccessTokenPage(@Valid OAuth2AccessTokenPageRequest request) {
        PageResult<OAuth2AccessTokenDO> pageResult = oauth2TokenService.getAccessTokenPage(request);
        return success(BeanUtils.toBean(pageResult, OAuth2AccessTokenResponse.class));
    }

    /**
     * 删除访问令牌
     *
     * <AUTHOR>
     * @date 2025/6/12 15:29
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:oauth2-token:delete')")
    public CommonResult<Boolean> deleteAccessToken(@RequestParam("accessToken") String accessToken) {
        authService.logout(accessToken, LoginLogTypeEnum.LOGOUT_DELETE.getType());
        return success(true);
    }

}
