package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.RolePageRequest;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;

@Mapper
public interface RoleMapper extends BaseMapperX<RoleDO> {

    default PageResult<RoleDO> selectPage(RolePageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<RoleDO>()
                .likeIfPresent(RoleDO::getName, request.getName())
                .likeIfPresent(RoleDO::getCode, request.getCode())
                .eqIfPresent(RoleDO::getStatus, request.getStatus())
                .betweenIfPresent(BaseDO::getCreateTime, request.getCreateTime())
                .orderByAsc(RoleDO::getSort));
    }

    default RoleDO selectByName(String name) {
        return selectOne(RoleDO::getName, name);
    }

    default RoleDO selectByCode(String code) {
        return selectOne(RoleDO::getCode, code);
    }

    default List<RoleDO> selectListByStatus(@Nullable Collection<Integer> statuses) {
        return selectList(RoleDO::getStatus, statuses);
    }

}
