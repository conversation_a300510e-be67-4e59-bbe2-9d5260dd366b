package com.qudaiji.cloud.p.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.common.constants.enums.common.SexEnum;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.user.request.UserPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.user.request.UserSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.user.request.UserUpdatePasswordRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.user.request.UserUpdateStatusRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserImportExcelVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserImportResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.user.response.UserSimpleResponse;
import com.qudaiji.cloud.p.module.system.convert.user.UserConvert;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import com.qudaiji.cloud.p.module.system.service.DeptService;
import com.qudaiji.cloud.p.module.system.service.SystemUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 管理后台 - 用户
 *
 * <AUTHOR>
 * @date 2025/6/12 16:05
 **/
@RestController
@RequestMapping("/system/user")
@Validated
public class SystemUserController {

    @Resource
    private SystemUserService userService;
    @Resource
    private DeptService deptService;

    /**
     * 新增用户
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:user:create')")
    public CommonResult<Long> createUser(@Valid @RequestBody UserSaveRequest request) {
        Long id = userService.createUser(request);
        return success(id);
    }

    /**
     * 修改用户
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @PutMapping("update")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserSaveRequest request) {
        userService.updateUser(request);
        return success(true);
    }

    /**
     * 删除用户
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        return success(true);
    }

    /**
     * 重置用户密码
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @PutMapping("/update-password")
    @PreAuthorize("@ss.hasPermission('system:user:update-password')")
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody UserUpdatePasswordRequest request) {
        userService.updateUserPassword(request.getId(), request.getPassword());
        return success(true);
    }

    /**
     * 修改用户状态
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody UserUpdateStatusRequest request) {
        userService.updateUserStatus(request.getId(), request.getStatus());
        return success(true);
    }

    /**
     * 获得用户分页列表
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:user:list')")
    public CommonResult<PageResult<UserResponse>> getUserPage(@Valid UserPageRequest request) {
        // 获得用户分页列表
        PageResult<SystemUserDO> pageResult = userService.getUserPage(request);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }
        // 拼接数据
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(
                convertList(pageResult.getList(), SystemUserDO::getDeptId));
        return success(new PageResult<>(UserConvert.INSTANCE.convertList(pageResult.getList(), deptMap),
                pageResult.getTotal()));
    }

    /**
     * 获得用户精简列表
     * 只包含被开启的用户，主要用于前端的下拉选项
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @GetMapping({"/list-all-simple", "/simple-list"})
    public CommonResult<List<UserSimpleResponse>> getSimpleUserList() {
        List<SystemUserDO> list = userService.getUserListByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 拼接数据
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(
                convertList(list, SystemUserDO::getDeptId));
        return success(UserConvert.INSTANCE.convertSimpleList(list, deptMap));
    }

    /**
     * 获得用户详情
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:user:query')")
    public CommonResult<UserResponse> getUser(@RequestParam("id") Long id) {
        SystemUserDO user = userService.getUser(id);
        if (user == null) {
            return success(null);
        }
        // 拼接数据
        DeptDO dept = deptService.getDept(user.getDeptId());
        return success(UserConvert.INSTANCE.convert(user, dept));
    }

    /**
     * 导出用户
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:user:export')")
    public void exportUserList(@Validated UserPageRequest request,
                               HttpServletResponse response) throws IOException {
        request.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SystemUserDO> list = userService.getUserPage(request).getList();
        // 输出 Excel
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(
                convertList(list, SystemUserDO::getDeptId));
        ExcelUtils.write(response, "用户数据.xls", "数据", UserResponse.class,
                UserConvert.INSTANCE.convertList(list, deptMap));
    }

    /**
     * 获得导入用户模板
     *
     * <AUTHOR>
     * @date 2025/6/12 16:06
     **/
    @GetMapping("/get-import-template")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<UserImportExcelVO> list = Arrays.asList(
                UserImportExcelVO.builder().username("yunai").deptId(1L).email("<EMAIL>").mobile("15601691300")
                        .nickname("芋道").status(CommonStatusEnum.ENABLE.getStatus()).sex(SexEnum.MALE.getSex()).build(),
                UserImportExcelVO.builder().username("yuanma").deptId(2L).email("<EMAIL>").mobile("15601701300")
                        .nickname("源码").status(CommonStatusEnum.DISABLE.getStatus()).sex(SexEnum.FEMALE.getSex()).build()
        );
        // 输出
        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserImportExcelVO.class, list);
    }

    /**
     * 导入用户
     *
     * @param file          Excel 文件
     * @param updateSupport 是否支持更新，默认为 false
     * <AUTHOR>
     * @date 2025/6/12 16:07
     **/
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermission('system:user:import')")
    public CommonResult<UserImportResponse> importExcel(@RequestParam("file") MultipartFile file,
                                                        @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
        return success(userService.importUserList(list, updateSupport));
    }

}
