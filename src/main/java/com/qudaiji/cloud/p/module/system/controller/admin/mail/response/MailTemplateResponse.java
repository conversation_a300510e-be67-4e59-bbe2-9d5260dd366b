package com.qudaiji.cloud.p.module.system.controller.admin.mail.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 邮件末班 Response
 */
@Data
public class MailTemplateResponse {

    /**
     * 编号
     * 示例值：1024
     */
    private Long id;

    /**
     * 模版名称
     * 示例值：测试名字
     */
    private String name;

    /**
     * 模版编号
     * 示例值：test
     */
    private String code;

    /**
     * 发送的邮箱账号编号
     * 示例值：1
     */
    private Long accountId;

    /**
     * 发送人名称
     * 示例值：芋头
     */
    private String nickname;

    /**
     * 标题
     * 示例值：注册成功
     */
    private String title;

    /**
     * 内容
     * 示例值：你好，注册成功啦
     */
    private String content;

    /**
     * 参数数组
     * 示例值：name,code
     */
    private List<String> params;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 示例值：1
     */
    private Integer status;

    /**
     * 备注
     * 示例值：奥特曼
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
