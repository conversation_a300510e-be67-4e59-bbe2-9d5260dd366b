package com.qudaiji.cloud.p.module.system.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplatePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplateSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.NotifyTemplateDO;
import jakarta.validation.Valid;

import java.util.Map;

/**
 * 站内信模版 Service 接口
 *
 * <AUTHOR>
 */
public interface NotifyTemplateService {

    /**
     * 创建站内信模版
     *
     * @param request 创建信息
     * @return 编号
     */
    Long createNotifyTemplate(@Valid NotifyTemplateSaveRequest request);

    /**
     * 更新站内信模版
     *
     * @param request 更新信息
     */
    void updateNotifyTemplate(@Valid NotifyTemplateSaveRequest request);

    /**
     * 删除站内信模版
     *
     * @param id 编号
     */
    void deleteNotifyTemplate(Long id);

    /**
     * 获得站内信模版
     *
     * @param id 编号
     * @return 站内信模版
     */
    NotifyTemplateDO getNotifyTemplate(Long id);

    /**
     * 获得站内信模板，从缓存中
     *
     * @param code 模板编码
     * @return 站内信模板
     */
    NotifyTemplateDO getNotifyTemplateByCodeFromCache(String code);

    /**
     * 获得站内信模版分页
     *
     * @param request 分页查询
     * @return 站内信模版分页
     */
    PageResult<NotifyTemplateDO> getNotifyTemplatePage(NotifyTemplatePageRequest request);

    /**
     * 格式化站内信内容
     *
     * @param content 站内信模板的内容
     * @param params 站内信内容的参数
     * @return 格式化后的内容
     */
    String formatNotifyTemplateContent(String content, Map<String, Object> params);

}
