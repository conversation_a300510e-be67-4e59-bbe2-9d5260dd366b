package com.qudaiji.cloud.p.module.system.controller.admin.dept.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import com.qudaiji.cloud.p.common.constants.enums.SystemDictTypeConstants;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 岗位信息 Response
 */
@Data
@ExcelIgnoreUnannotated
public class PostResponse {

    /**
     * 岗位序号
     * 必填：是
     * 示例：1024
     */
    @ExcelProperty("岗位序号")
    private Long id;

    /**
     * 岗位名称
     * 必填：是
     * 示例：小土豆
     */
    @ExcelProperty("岗位名称")
    private String name;

    /**
     * 岗位编码
     * 必填：是
     * 示例：jqm-p-service
     */
    @ExcelProperty("岗位编码")
    private String code;

    /**
     * 显示顺序
     * 必填：是
     * 示例：1024
     */
    @ExcelProperty("岗位排序")
    private Integer sort;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     * 必填：是
     * 示例：1
     */
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.COMMON_STATUS)
    private Integer status;

    /**
     * 备注
     * 示例：快乐的备注
     */
    private String remark;

    /**
     * 创建时间
     * 必填：是
     */
    private LocalDateTime createTime;

}
