package com.qudaiji.cloud.p.module.system.controller.admin.mail.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 管理后台 - 邮件发送 Request
 */
@Data
public class MailTemplateSendRequest {

    /**
     * 接收邮箱
     * 例如：<EMAIL>
     */
    @NotEmpty(message = "接收邮箱不能为空")
    private String mail;

    /**
     * 模板编码
     * 例如：test_01
     */
    @NotNull(message = "模板编码不能为空")
    private String templateCode;

    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;

}
