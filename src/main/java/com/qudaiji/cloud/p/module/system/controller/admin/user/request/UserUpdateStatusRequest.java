package com.qudaiji.cloud.p.module.system.controller.admin.user.request;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.validation.InEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 管理后台 - 用户更新状态 Request
 */
@Data
public class UserUpdateStatusRequest {

    /**
     * 用户编号
     * 例如：1024
     */
    @NotNull(message = "角色编号不能为空")
    private Long id;

    /**
     * 状态，见 CommonStatusEnum 枚举
     * 例如：1
     */
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

}
