package com.qudaiji.cloud.p.module.system.service.impl;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailAccountPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailAccountSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.MailAccountDO;
import com.qudaiji.cloud.p.module.system.mapper.MailAccountMapper;
import com.qudaiji.cloud.p.common.constants.redis.RedisKeyConstants;
import com.qudaiji.cloud.p.module.system.service.MailAccountService;
import com.qudaiji.cloud.p.module.system.service.MailTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.p.common.constants.enums.SystemErrorCodeConstants.MAIL_ACCOUNT_NOT_EXISTS;
import static com.qudaiji.cloud.p.common.constants.enums.SystemErrorCodeConstants.MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS;

/**
 * 邮箱账号 Service 实现类
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Service
@Validated
@Slf4j
public class MailAccountServiceImpl implements MailAccountService {

    @Resource
    private MailAccountMapper mailAccountMapper;

    @Resource
    private MailTemplateService mailTemplateService;

    @Override
    public Long createMailAccount(MailAccountSaveRequest request) {
        MailAccountDO account = BeanUtils.toBean(request, MailAccountDO.class);
        mailAccountMapper.insert(account);
        return account.getId();
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.MAIL_ACCOUNT, key = "#request.id")
    public void updateMailAccount(MailAccountSaveRequest request) {
        // 校验是否存在
        validateMailAccountExists(request.getId());

        // 更新
        MailAccountDO updateObj = BeanUtils.toBean(request, MailAccountDO.class);
        mailAccountMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.MAIL_ACCOUNT, key = "#id")
    public void deleteMailAccount(Long id) {
        // 校验是否存在账号
        validateMailAccountExists(id);
        // 校验是否存在关联模版
        if (mailTemplateService.getMailTemplateCountByAccountId(id) > 0) {
            throw exception(MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS);
        }

        // 删除
        mailAccountMapper.deleteById(id);
    }

    private void validateMailAccountExists(Long id) {
        if (mailAccountMapper.selectById(id) == null) {
            throw exception(MAIL_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public MailAccountDO getMailAccount(Long id) {
        return mailAccountMapper.selectById(id);
    }

    @Override
    @Cacheable(value = RedisKeyConstants.MAIL_ACCOUNT, key = "#id", unless = "#result == null")
    public MailAccountDO getMailAccountFromCache(Long id) {
        return getMailAccount(id);
    }

    @Override
    public PageResult<MailAccountDO> getMailAccountPage(MailAccountPageRequest request) {
        return mailAccountMapper.selectPage(request);
    }

    @Override
    public List<MailAccountDO> getMailAccountList() {
        return mailAccountMapper.selectList();
    }

}
