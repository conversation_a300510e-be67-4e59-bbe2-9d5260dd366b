package com.qudaiji.cloud.p.module.system.convert.auth;

import cn.hutool.core.collection.CollUtil;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.qudaiji.cloud.p.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.qudaiji.cloud.p.module.system.api.social.dto.SocialUserBindReqDTO;
import com.qudaiji.cloud.p.common.constants.enums.permission.MenuTypeEnum;
import com.qudaiji.cloud.p.module.system.controller.admin.auth.request.*;
import com.qudaiji.cloud.p.module.system.controller.admin.auth.response.AuthLoginResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.auth.response.AuthPermissionInfoResponse;
import com.qudaiji.cloud.p.module.system.entity.OAuth2AccessTokenDO;
import com.qudaiji.cloud.p.module.system.entity.MenuDO;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.qudaiji.cloud.framework.common.util.collection.CollectionUtils.convertSet;
import static com.qudaiji.cloud.framework.common.util.collection.CollectionUtils.filterList;
import static com.qudaiji.cloud.p.module.system.entity.MenuDO.ID_ROOT;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    AuthLoginResponse convert(OAuth2AccessTokenDO bean);

    default AuthPermissionInfoResponse convert(SystemUserDO user, List<RoleDO> roleList, List<MenuDO> menuList) {
        return AuthPermissionInfoResponse.builder()
                .user(BeanUtils.toBean(user, AuthPermissionInfoResponse.UserVO.class))
                .roles(convertSet(roleList, RoleDO::getCode))
                // 权限标识信息
                .permissions(convertSet(menuList, MenuDO::getPermission))
                // 菜单树
                .menus(buildMenuTree(menuList))
                .build();
    }

    AuthPermissionInfoResponse.MenuVO convertTreeNode(MenuDO menu);

    /**
     * 将菜单列表，构建成菜单树
     *
     * @param menuList 菜单列表
     * @return 菜单树
     */
    default List<AuthPermissionInfoResponse.MenuVO> buildMenuTree(List<MenuDO> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return Collections.emptyList();
        }
        // 移除按钮
        menuList.removeIf(menu -> menu.getType().equals(MenuTypeEnum.BUTTON.getType()));
        // 排序，保证菜单的有序性
        menuList.sort(Comparator.comparing(MenuDO::getSort));

        // 构建菜单树
        // 使用 LinkedHashMap 的原因，是为了排序 。实际也可以用 Stream API ，就是太丑了。
        Map<Long, AuthPermissionInfoResponse.MenuVO> treeNodeMap = new LinkedHashMap<>();
        menuList.forEach(menu -> treeNodeMap.put(menu.getId(), AuthConvert.INSTANCE.convertTreeNode(menu)));
        // 处理父子关系
        treeNodeMap.values().stream().filter(node -> !node.getParentId().equals(ID_ROOT)).forEach(childNode -> {
            // 获得父节点
            AuthPermissionInfoResponse.MenuVO parentNode = treeNodeMap.get(childNode.getParentId());
            if (parentNode == null) {
                LoggerFactory.getLogger(getClass()).error("[buildRouterTree][resource({}) 找不到父资源({})]",
                        childNode.getId(), childNode.getParentId());
                return;
            }
            // 将自己添加到父节点中
            if (parentNode.getChildren() == null) {
                parentNode.setChildren(new ArrayList<>());
            }
            parentNode.getChildren().add(childNode);
        });
        // 获得到所有的根节点
        return filterList(treeNodeMap.values(), node -> ID_ROOT.equals(node.getParentId()));
    }

    SocialUserBindReqDTO convert(Long userId, Integer userType, AuthSocialLoginRequest request);

    SmsCodeSendReqDTO convert(AuthSmsSendRequest request);

    SmsCodeUseReqDTO convert(AuthSmsLoginRequest authSmsLoginRequest, Integer scene, String usedIp);

}
