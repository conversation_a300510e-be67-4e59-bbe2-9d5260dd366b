package com.qudaiji.cloud.p.module.infra.controller.admin.file;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.response.FileConfigResponse;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FileConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.entity.FileConfigDO;
import com.qudaiji.cloud.p.module.infra.service.FileConfigService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 文件配置
 *
 * <AUTHOR>
 * @date 2025/6/12 15:38
 **/
@RestController
@RequestMapping("/infra/file-config")
@Validated
public class FileConfigController {

    @Resource
    private FileConfigService fileConfigService;

    /**
     * 创建文件配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('infra:file-config:create')")
    public CommonResult<Long> createFileConfig(@Valid @RequestBody FileConfigSaveRequest request) {
        return success(fileConfigService.createFileConfig(request));
    }

    /**
     * 更新文件配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('infra:file-config:update')")
    public CommonResult<Boolean> updateFileConfig(@Valid @RequestBody FileConfigSaveRequest request) {
        fileConfigService.updateFileConfig(request);
        return success(true);
    }

    /**
     * 更新文件配置为 Master
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @PutMapping("/update-master")
    @PreAuthorize("@ss.hasPermission('infra:file-config:update')")
    public CommonResult<Boolean> updateFileConfigMaster(@RequestParam("id") Long id) {
        fileConfigService.updateFileConfigMaster(id);
        return success(true);
    }


    /**
     * 删除文件配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('infra:file-config:delete')")
    public CommonResult<Boolean> deleteFileConfig(@RequestParam("id") Long id) {
        fileConfigService.deleteFileConfig(id);
        return success(true);
    }

    /**
     * 获得文件配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<FileConfigResponse> getFileConfig(@RequestParam("id") Long id) {
        FileConfigDO config = fileConfigService.getFileConfig(id);
        return success(BeanUtils.toBean(config, FileConfigResponse.class));
    }

    /**
     * 获得文件配置分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<PageResult<FileConfigResponse>> getFileConfigPage(@Valid FileConfigPageRequest pageVO) {
        PageResult<FileConfigDO> pageResult = fileConfigService.getFileConfigPage(pageVO);
        return success(BeanUtils.toBean(pageResult, FileConfigResponse.class));
    }

    /**
     * 测试文件配置是否正确
     *
     * <AUTHOR>
     * @date 2025/6/12 15:38
     **/
    @GetMapping("/test")
    @PreAuthorize("@ss.hasPermission('infra:file-config:query')")
    public CommonResult<String> testFileConfig(@RequestParam("id") Long id) throws Exception {
        String url = fileConfigService.testFileConfig(id);
        return success(url);
    }
}
