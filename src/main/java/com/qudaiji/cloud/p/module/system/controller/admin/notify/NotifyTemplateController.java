package com.qudaiji.cloud.p.module.system.controller.admin.notify;

import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplatePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.response.NotifyTemplateResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplateSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyTemplateSendRequest;
import com.qudaiji.cloud.p.module.system.entity.NotifyTemplateDO;
import com.qudaiji.cloud.p.module.system.service.NotifySendService;
import com.qudaiji.cloud.p.module.system.service.NotifyTemplateService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 站内信模版
 *
 * <AUTHOR>
 * @date 2025/6/12 16:15
 **/
@RestController
@RequestMapping("/system/notify-template")
@Validated
public class NotifyTemplateController {

    @Resource
    private NotifyTemplateService notifyTemplateService;

    @Resource
    private NotifySendService notifySendService;

    /**
     * 创建站内信模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:notify-template:create')")
    public CommonResult<Long> createNotifyTemplate(@Valid @RequestBody NotifyTemplateSaveRequest request) {
        return success(notifyTemplateService.createNotifyTemplate(request));
    }

    /**
     * 更新站内信模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:notify-template:update')")
    public CommonResult<Boolean> updateNotifyTemplate(@Valid @RequestBody NotifyTemplateSaveRequest request) {
        notifyTemplateService.updateNotifyTemplate(request);
        return success(true);
    }

    /**
     * 删除站内信模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:notify-template:delete')")
    public CommonResult<Boolean> deleteNotifyTemplate(@RequestParam("id") Long id) {
        notifyTemplateService.deleteNotifyTemplate(id);
        return success(true);
    }

    /**
     * 获得站内信模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:notify-template:query')")
    public CommonResult<NotifyTemplateResponse> getNotifyTemplate(@RequestParam("id") Long id) {
        NotifyTemplateDO template = notifyTemplateService.getNotifyTemplate(id);
        return success(BeanUtils.toBean(template, NotifyTemplateResponse.class));
    }

    /**
     * 获得站内信模版分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:notify-template:query')")
    public CommonResult<PageResult<NotifyTemplateResponse>> getNotifyTemplatePage(@Valid NotifyTemplatePageRequest pageVO) {
        PageResult<NotifyTemplateDO> pageResult = notifyTemplateService.getNotifyTemplatePage(pageVO);
        return success(BeanUtils.toBean(pageResult, NotifyTemplateResponse.class));
    }

    /**
     * 发送站内信
     *
     * <AUTHOR>
     * @date 2025/6/12 16:15
     **/
    @PostMapping("/send-notify")
    @PreAuthorize("@ss.hasPermission('system:notify-template:send-notify')")
    public CommonResult<Long> sendNotify(@Valid @RequestBody NotifyTemplateSendRequest request) {
        if (UserTypeEnum.MEMBER.getValue().equals(request.getUserType())) {
            return success(notifySendService.sendSingleNotifyToMember(request.getUserId(),
                    request.getTemplateCode(), request.getTemplateParams()));
        } else {
            return success(notifySendService.sendSingleNotifyToAdmin(request.getUserId(),
                    request.getTemplateCode(), request.getTemplateParams()));
        }
    }
}
