package com.qudaiji.cloud.p.module.system.controller.admin.user.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 管理后台 - 用户导入 Response
 *
 * <AUTHOR>
 * @date 2025/6/12 15:15
 **/
@Data
@Builder
public class UserImportResponse {

    /**
     * 创建成功的用户名数组
     **/
    private List<String> createUsernames;

    /**
     * 更新成功的用户名数组
     **/
    private List<String> updateUsernames;

    /**
     * 导入失败的用户集合，key 为用户名，value 为失败原因
     **/
    private Map<String, String> failureUsernames;

}
