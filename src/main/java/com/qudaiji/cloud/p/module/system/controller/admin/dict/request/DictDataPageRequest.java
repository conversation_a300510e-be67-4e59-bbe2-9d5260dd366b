package com.qudaiji.cloud.p.module.system.controller.admin.dict.request;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.validation.InEnum;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理后台 - 字典类型分页列表 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataPageRequest extends PageParam {

    /**
     * 字典标签
     */
    @Size(max = 100, message = "字典标签长度不能超过100个字符")
    private String label;

    /**
     * 字典类型，模糊匹配
     * 例如：sys_common_sex
     */
    @Size(max = 100, message = "字典类型类型长度不能超过100个字符")
    private String dictType;

    /**
     * 展示状态，参见 CommonStatusEnum 枚举类
     * 例如：1
     */
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

}
