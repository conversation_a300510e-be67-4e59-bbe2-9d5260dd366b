package com.qudaiji.cloud.p.module.infra.controller.admin.config;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigResponse;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.convert.config.ConfigConvert;
import com.qudaiji.cloud.p.module.infra.entity.ConfigDO;
import com.qudaiji.cloud.p.common.constants.enums.InfraErrorCodeConstants;
import com.qudaiji.cloud.p.module.infra.service.ConfigService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;


/**
 * 管理后台 - 参数配置
 *
 * <AUTHOR>
 * @date 2025/6/12 15:49
 **/
@RestController
@RequestMapping("/infra/config")
@Validated
public class ConfigController {

    @Resource
    private ConfigService configService;

    /**
     * 创建参数配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('infra:config:create')")
    public CommonResult<Long> createConfig(@Valid @RequestBody ConfigSaveRequest request) {
        return success(configService.createConfig(request));
    }

    /**
     * 修改参数配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('infra:config:update')")
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody ConfigSaveRequest request) {
        configService.updateConfig(request);
        return success(true);
    }

    /**
     * 删除参数配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('infra:config:delete')")
    public CommonResult<Boolean> deleteConfig(@RequestParam("id") Long id) {
        configService.deleteConfig(id);
        return success(true);
    }

    /**
     * 获得参数配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @GetMapping(value = "/get")
    @PreAuthorize("@ss.hasPermission('infra:config:query')")
    public CommonResult<ConfigResponse> getConfig(@RequestParam("id") Long id) {
        return success(ConfigConvert.INSTANCE.convert(configService.getConfig(id)));
    }

    /**
     * 根据参数键名查询参数值,不可见的配置，不允许返回给前端
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @GetMapping(value = "/get-value-by-key")
    public CommonResult<String> getConfigKey(@RequestParam("key") String key) {
        ConfigDO config = configService.getConfigByKey(key);
        if (config == null) {
            return success(null);
        }
        if (!config.getVisible()) {
            throw exception(InfraErrorCodeConstants.CONFIG_GET_VALUE_ERROR_IF_VISIBLE);
        }
        return success(config.getValue());
    }

    /**
     * 获得参数配置分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:49
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('infra:config:query')")
    public CommonResult<PageResult<ConfigResponse>> getConfigPage(@Valid ConfigPageRequest request) {
        PageResult<ConfigDO> page = configService.getConfigPage(request);
        return success(ConfigConvert.INSTANCE.convertPage(page));
    }

    /**
     * 导出参数配置
     *
     * <AUTHOR>
     * @date 2025/6/12 15:50
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('infra:config:export')")
    public void exportConfig(ConfigPageRequest request,
                             HttpServletResponse response) throws IOException {
        request.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ConfigDO> list = configService.getConfigPage(request).getList();
        // 输出
        ExcelUtils.write(response, "参数配置.xls", "数据", ConfigResponse.class,
                ConfigConvert.INSTANCE.convertList(list));
    }

}
