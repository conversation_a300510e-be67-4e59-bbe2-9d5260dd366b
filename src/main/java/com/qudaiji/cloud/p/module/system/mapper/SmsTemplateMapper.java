package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsTemplatePageRequest;
import com.qudaiji.cloud.p.module.system.entity.SmsTemplateDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SmsTemplateMapper extends BaseMapperX<SmsTemplateDO> {

    default SmsTemplateDO selectByCode(String code) {
        return selectOne(SmsTemplateDO::getCode, code);
    }

    default PageResult<SmsTemplateDO> selectPage(SmsTemplatePageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<SmsTemplateDO>()
                .eqIfPresent(SmsTemplateDO::getType, request.getType())
                .eqIfPresent(SmsTemplateDO::getStatus, request.getStatus())
                .likeIfPresent(SmsTemplateDO::getCode, request.getCode())
                .likeIfPresent(SmsTemplateDO::getContent, request.getContent())
                .likeIfPresent(SmsTemplateDO::getApiTemplateId, request.getApiTemplateId())
                .eqIfPresent(SmsTemplateDO::getChannelId, request.getChannelId())
                .betweenIfPresent(SmsTemplateDO::getCreateTime, request.getCreateTime())
                .orderByDesc(SmsTemplateDO::getId));
    }

    default Long selectCountByChannelId(Long channelId) {
        return selectCount(SmsTemplateDO::getChannelId, channelId);
    }

}
