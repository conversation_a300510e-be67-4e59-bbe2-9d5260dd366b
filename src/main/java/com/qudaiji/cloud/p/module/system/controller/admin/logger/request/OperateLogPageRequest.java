package com.qudaiji.cloud.p.module.system.controller.admin.logger.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.qudaiji.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 操作日志分页列表 Request
 */
@Data
public class OperateLogPageRequest extends PageParam {

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 操作模块业务编号
     */
    private Long bizId;

    /**
     * 操作模块，模拟匹配
     */
    private String type;

    /**
     * 操作名，模拟匹配
     */
    private String subType;

    /**
     * 操作明细，模拟匹配
     */
    private String action;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
