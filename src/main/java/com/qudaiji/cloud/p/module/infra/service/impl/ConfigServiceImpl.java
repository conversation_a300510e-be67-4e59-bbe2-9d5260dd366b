package com.qudaiji.cloud.p.module.infra.service.impl;

import com.google.common.annotations.VisibleForTesting;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.common.constants.enums.ConfigTypeEnum;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigPageRequest;
import com.qudaiji.cloud.p.module.infra.controller.admin.config.request.ConfigSaveRequest;
import com.qudaiji.cloud.p.module.infra.convert.config.ConfigConvert;
import com.qudaiji.cloud.p.module.infra.entity.ConfigDO;
import com.qudaiji.cloud.p.module.infra.mapper.ConfigMapper;
import com.qudaiji.cloud.p.module.infra.service.ConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.p.common.constants.enums.InfraErrorCodeConstants.*;

/**
 * 参数配置 Service 实现类
 */
@Service
@Slf4j
@Validated
public class ConfigServiceImpl implements ConfigService {

    @Resource
    private ConfigMapper configMapper;

    @Override
    public Long createConfig(ConfigSaveRequest request) {
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(null, request.getKey());

        // 插入参数配置
        ConfigDO config = ConfigConvert.INSTANCE.convert(request);
        config.setType(ConfigTypeEnum.CUSTOM.getType());
        configMapper.insert(config);
        return config.getId();
    }

    @Override
    public void updateConfig(ConfigSaveRequest request) {
        // 校验自己存在
        validateConfigExists(request.getId());
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(request.getId(), request.getKey());

        // 更新参数配置
        ConfigDO updateObj = ConfigConvert.INSTANCE.convert(request);
        configMapper.updateById(updateObj);
    }

    @Override
    public void deleteConfig(Long id) {
        // 校验配置存在
        ConfigDO config = validateConfigExists(id);
        // 内置配置，不允许删除
        if (ConfigTypeEnum.SYSTEM.getType().equals(config.getType())) {
            throw exception(CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE);
        }
        // 删除
        configMapper.deleteById(id);
    }

    @Override
    public ConfigDO getConfig(Long id) {
        return configMapper.selectById(id);
    }

    @Override
    public ConfigDO getConfigByKey(String key) {
        return configMapper.selectByKey(key);
    }

    @Override
    public PageResult<ConfigDO> getConfigPage(ConfigPageRequest request) {
        return configMapper.selectPage(request);
    }

    @VisibleForTesting
    public ConfigDO validateConfigExists(Long id) {
        if (id == null) {
            return null;
        }
        ConfigDO config = configMapper.selectById(id);
        if (config == null) {
            throw exception(CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @VisibleForTesting
    public void validateConfigKeyUnique(Long id, String key) {
        ConfigDO config = configMapper.selectByKey(key);
        if (config == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的参数配置
        if (id == null) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
        if (!config.getId().equals(id)) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
    }

}
