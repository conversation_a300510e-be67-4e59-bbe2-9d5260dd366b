package com.qudaiji.cloud.p.module.system.controller.admin.mail.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 邮箱账号分页 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MailAccountPageRequest extends PageParam {

    /**
     * 邮箱
     */
    private String mail;

    /**
     * 用户名
     */
    private String username;

}
