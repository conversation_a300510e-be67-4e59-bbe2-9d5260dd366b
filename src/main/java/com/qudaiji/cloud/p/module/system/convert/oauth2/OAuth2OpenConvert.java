package com.qudaiji.cloud.p.module.system.convert.oauth2;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.qudaiji.cloud.framework.common.core.KeyValue;
import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.util.collection.CollectionUtils;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response.OAuth2OpenAccessTokenResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response.OAuth2OpenAuthorizeInfoResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response.OAuth2OpenCheckTokenResponse;
import com.qudaiji.cloud.p.module.system.entity.OAuth2AccessTokenDO;
import com.qudaiji.cloud.p.module.system.entity.OAuth2ApproveDO;
import com.qudaiji.cloud.p.module.system.entity.OAuth2ClientDO;
import com.qudaiji.cloud.p.common.utils.OAuth2Utils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Mapper
public interface OAuth2OpenConvert {

    OAuth2OpenConvert INSTANCE = Mappers.getMapper(OAuth2OpenConvert.class);

    default OAuth2OpenAccessTokenResponse convert(OAuth2AccessTokenDO bean) {
        OAuth2OpenAccessTokenResponse response = BeanUtils.toBean(bean, OAuth2OpenAccessTokenResponse.class);
        response.setTokenType(SecurityFrameworkUtils.AUTHORIZATION_BEARER.toLowerCase());
        response.setExpiresIn(OAuth2Utils.getExpiresIn(bean.getExpiresTime()));
        response.setScope(OAuth2Utils.buildScopeStr(bean.getScopes()));
        return response;
    }

    default OAuth2OpenCheckTokenResponse convert2(OAuth2AccessTokenDO bean) {
        OAuth2OpenCheckTokenResponse response = BeanUtils.toBean(bean, OAuth2OpenCheckTokenResponse.class);
        response.setExp(LocalDateTimeUtil.toEpochMilli(bean.getExpiresTime()) / 1000L);
        response.setUserType(UserTypeEnum.ADMIN.getValue());
        return response;
    }

    default OAuth2OpenAuthorizeInfoResponse convert(OAuth2ClientDO client, List<OAuth2ApproveDO> approves) {
        // 构建 scopes
        List<KeyValue<String, Boolean>> scopes = new ArrayList<>(client.getScopes().size());
        Map<String, OAuth2ApproveDO> approveMap = CollectionUtils.convertMap(approves, OAuth2ApproveDO::getScope);
        client.getScopes().forEach(scope -> {
            OAuth2ApproveDO approve = approveMap.get(scope);
            scopes.add(new KeyValue<>(scope, approve != null ? approve.getApproved() : false));
        });
        // 拼接返回
        return new OAuth2OpenAuthorizeInfoResponse(
                new OAuth2OpenAuthorizeInfoResponse.Client(client.getName(), client.getLogo()), scopes);
    }

}
