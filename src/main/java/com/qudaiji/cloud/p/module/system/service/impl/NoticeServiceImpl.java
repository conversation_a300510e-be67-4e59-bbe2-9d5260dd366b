package com.qudaiji.cloud.p.module.system.service.impl;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticeSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.NoticeDO;
import com.qudaiji.cloud.p.module.system.mapper.NoticeMapper;
import com.google.common.annotations.VisibleForTesting;
import com.qudaiji.cloud.p.module.system.service.NoticeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.p.common.constants.enums.SystemErrorCodeConstants.NOTICE_NOT_FOUND;

/**
 * 通知公告 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class NoticeServiceImpl implements NoticeService {

    @Resource
    private NoticeMapper noticeMapper;

    @Override
    public Long createNotice(NoticeSaveRequest request) {
        NoticeDO notice = BeanUtils.toBean(request, NoticeDO.class);
        noticeMapper.insert(notice);
        return notice.getId();
    }

    @Override
    public void updateNotice(NoticeSaveRequest request) {
        // 校验是否存在
        validateNoticeExists(request.getId());
        // 更新通知公告
        NoticeDO updateObj = BeanUtils.toBean(request, NoticeDO.class);
        noticeMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotice(Long id) {
        // 校验是否存在
        validateNoticeExists(id);
        // 删除通知公告
        noticeMapper.deleteById(id);
    }

    @Override
    public PageResult<NoticeDO> getNoticePage(NoticePageRequest request) {
        return noticeMapper.selectPage(request);
    }

    @Override
    public NoticeDO getNotice(Long id) {
        return noticeMapper.selectById(id);
    }

    @VisibleForTesting
    public void validateNoticeExists(Long id) {
        if (id == null) {
            return;
        }
        NoticeDO notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw exception(NOTICE_NOT_FOUND);
        }
    }

}
