package com.qudaiji.cloud.p.module.system.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notice.request.NoticeSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.NoticeDO;

/**
 * 通知公告 Service 接口
 */
public interface NoticeService {

    /**
     * 创建通知公告
     *
     * @param request 通知公告
     * @return 编号
     */
    Long createNotice(NoticeSaveRequest request);

    /**
     * 更新通知公告
     *
     * @param request 通知公告
     */
    void updateNotice(NoticeSaveRequest request);

    /**
     * 删除通知公告
     *
     * @param id 编号
     */
    void deleteNotice(Long id);

    /**
     * 获得通知公告分页列表
     *
     * @param request 分页条件
     * @return 部门分页列表
     */
    PageResult<NoticeDO> getNoticePage(NoticePageRequest request);

    /**
     * 获得通知公告
     *
     * @param id 编号
     * @return 通知公告
     */
    NoticeDO getNotice(Long id);

}
