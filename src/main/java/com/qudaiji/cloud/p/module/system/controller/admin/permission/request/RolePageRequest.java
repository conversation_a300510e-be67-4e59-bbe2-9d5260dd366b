package com.qudaiji.cloud.p.module.system.controller.admin.permission.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.qudaiji.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
public class RolePageRequest extends PageParam {

    /**
     * 角色名称，模糊匹配
     **/
    private String name;

    /**
     * 角色标识，模糊匹配
     **/
    private String code;
    /**
     * 展示状态，参见 CommonStatusEnum 枚举类
     **/
    private Integer status;

    /**
     * 创建时间,[2022-07-01 00:00:00,2022-07-01 23:59:59]
     **/
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
