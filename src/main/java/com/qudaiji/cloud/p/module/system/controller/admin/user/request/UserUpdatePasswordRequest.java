package com.qudaiji.cloud.p.module.system.controller.admin.user.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 管理后台 - 用户更新密码 Request
 */
@Data
public class UserUpdatePasswordRequest {

    /**
     * 用户编号
     * 例如：1024
     */
    @NotNull(message = "用户编号不能为空")
    private Long id;

    /**
     * 密码
     * 例如：123456
     */
    @NotEmpty(message = "密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password;

}
