package com.qudaiji.cloud.p.module.system.controller.admin.permission;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.RolePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.response.RoleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.RoleSaveRequest;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.service.RoleService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static java.util.Collections.singleton;

/**
 * 管理后台 - 角色
 *
 * <AUTHOR>
 * @date 2025/6/12 15:51
 **/
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {

    @Resource
    private RoleService roleService;

    /**
     * 创建角色
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createRole(@Valid @RequestBody RoleSaveRequest request) {
        return success(roleService.createRole(request, null));
    }

    /**
     * 修改角色
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:role:update')")
    public CommonResult<Boolean> updateRole(@Valid @RequestBody RoleSaveRequest request) {
        roleService.updateRole(request);
        return success(true);
    }

    /**
     * 删除角色
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:role:delete')")
    public CommonResult<Boolean> deleteRole(@RequestParam("id") Long id) {
        roleService.deleteRole(id);
        return success(true);
    }

    /**
     * 获得角色信息
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<RoleResponse> getRole(@RequestParam("id") Long id) {
        RoleDO role = roleService.getRole(id);
        return success(BeanUtils.toBean(role, RoleResponse.class));
    }

    /**
     * 获得角色分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<PageResult<RoleResponse>> getRolePage(RolePageRequest request) {
        PageResult<RoleDO> pageResult = roleService.getRolePage(request);
        return success(BeanUtils.toBean(pageResult, RoleResponse.class));
    }

    /**
     * 获取角色精简信息列表
     * 只包含被开启的角色，主要用于前端的下拉选项
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    public CommonResult<List<RoleResponse>> getSimpleRoleList() {
        List<RoleDO> list = roleService.getRoleListByStatus(singleton(CommonStatusEnum.ENABLE.getStatus()));
        list.sort(Comparator.comparing(RoleDO::getSort));
        return success(BeanUtils.toBean(list, RoleResponse.class));
    }

    /**
     * 导出角色 Excel
     *
     * <AUTHOR>
     * @date 2025/6/12 15:51
     **/
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('system:role:export')")
    public void export(HttpServletResponse response, @Validated RolePageRequest request) throws IOException {
        request.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RoleDO> list = roleService.getRolePage(request).getList();
        // 输出
        ExcelUtils.write(response, "角色数据.xls", "数据", RoleResponse.class,
                BeanUtils.toBean(list, RoleResponse.class));
    }

}
