package com.qudaiji.cloud.p.module.infra.controller.admin.db.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 数据源配置 Response
 */
@Data
public class DataSourceConfigResponse {

    /**
     * 主键编号
     */
    private Integer id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源连接
     */
    private String url;

    /**
     * 用户名
     */
    private String username;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
