package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 管理后台 - OAuth2 获得用户基本信息 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2UserInfoResponse {

    /**
     * 用户编号
     * 必填，例如：1
     */
    private Long id;

    /**
     * 用户账号
     * 必填，例如：芋艿
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户邮箱
     * 例如：<EMAIL>
     */
    private String email;
    /**
     * 手机号码
     * 例如：15601691300
     */
    private String mobile;

    /**
     * 用户性别，参见 SexEnum 枚举类
     * 例如：1
     */
    private Integer sex;

    /**
     * 用户头像
     * 例如：https://www.iocoder.cn/xxx.png
     */
    private String avatar;

    /**
     * 所在部门
     */
    private Dept dept;

    /**
     * 所属岗位数组
     */
    private List<Post> posts;

    /**
     * 部门
     */
    @Data
    public static class Dept {

        /**
         * 部门编号
         * 必填，例如：1
         */
        private Long id;

        /**
         * 部门名称
         * 必填，例如：研发部
         */
        private String name;

    }

    /**
     * 岗位
     */
    @Data
    public static class Post {

        /**
         * 岗位编号
         * 必填，例如：1
         */
        private Long id;

        /**
         * 岗位名称
         * 必填，例如：开发
         */
        private String name;

    }

}
