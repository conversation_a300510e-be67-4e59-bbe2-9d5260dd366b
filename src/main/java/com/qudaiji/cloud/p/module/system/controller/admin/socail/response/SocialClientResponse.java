package com.qudaiji.cloud.p.module.system.controller.admin.socail.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 社交客户端 Response
 */
@Data
public class SocialClientResponse {

    /**
     * 编号
     * 例如：27162
     */
    private Long id;

    /**
     * 应用名
     * 例如：jqm-p-service商城
     */
    private String name;

    /**
     * 社交平台的类型
     * 例如：31
     */
    private Integer socialType;

    /**
     * 用户类型
     * 例如：2
     */
    private Integer userType;

    /**
     * 客户端编号
     * 例如：wwd411c69a39ad2e54
     */
    private String clientId;

    /**
     * 客户端密钥
     * 例如：peter
     */
    private String clientSecret;

    /**
     * 授权方的网页应用编号
     * 例如：2000045
     */
    private String agentId;

    /**
     * 状态
     * 例如：1
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
