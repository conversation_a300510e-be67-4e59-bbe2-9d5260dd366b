package com.qudaiji.cloud.p.module.infra.controller.admin.config.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.constants.enums.InfraDictTypeConstants;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 参数配置信息
 *
 * <AUTHOR>
 * @date 2025/6/12 13:08
 **/
@Data
@ExcelIgnoreUnannotated
public class ConfigResponse {

    @ExcelProperty("参数配置序号")
    private Long id;

    @ExcelProperty("参数分类")
    private String category;

    @ExcelProperty("参数名称")
    private String name;

    @ExcelProperty("参数键名")
    private String key;

    @ExcelProperty("参数键值")
    private String value;

    @ExcelProperty(value = "参数类型", converter = DictConvert.class)
    @DictFormat(InfraDictTypeConstants.CONFIG_TYPE)
    private Integer type;

    @ExcelProperty(value = "是否可见", converter = DictConvert.class)
    @DictFormat(InfraDictTypeConstants.BOOLEAN_STRING)
    private Boolean visible;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
