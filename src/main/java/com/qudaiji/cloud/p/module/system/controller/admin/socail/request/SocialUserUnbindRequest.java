package com.qudaiji.cloud.p.module.system.controller.admin.socail.request;

import com.qudaiji.cloud.framework.common.validation.InEnum;
import com.qudaiji.cloud.p.common.constants.enums.social.SocialTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理后台 - 取消社交绑定 Request
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SocialUserUnbindRequest {

    /**
     * 社交平台的类型，参见 UserSocialTypeEnum 枚举值
     * 示例：10
     */
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平台的类型不能为空")
    private Integer type;

    /**
     * 社交用户的 openid
     * 示例：IPRmJ0wvBptiPIlGEZiPewGwiEiE
     */
    @NotEmpty(message = "社交用户的 openid 不能为空")
    private String openid;

}
