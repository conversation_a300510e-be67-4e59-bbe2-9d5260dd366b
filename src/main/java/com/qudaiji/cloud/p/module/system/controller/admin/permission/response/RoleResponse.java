package com.qudaiji.cloud.p.module.system.controller.admin.permission.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import com.qudaiji.cloud.p.common.constants.enums.SystemDictTypeConstants;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 管理后台 - 角色信息 Response
 */
@Data
@ExcelIgnoreUnannotated
public class RoleResponse {

    /**
     * 角色编号
     * 例如：1
     */
    @ExcelProperty("角色序号")
    private Long id;

    /**
     * 角色名称
     * 例如：管理员
     */
    @ExcelProperty("角色名称")
    private String name;

    /**
     * 角色标志
     * 例如：admin
     */
    @NotBlank(message = "角色标志不能为空")
    @ExcelProperty("角色标志")
    private String code;

    /**
     * 显示顺序
     * 例如：1024
     */
    @ExcelProperty("角色排序")
    private Integer sort;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     * 例如：1
     */
    @ExcelProperty(value = "角色状态", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.COMMON_STATUS)
    private Integer status;

    /**
     * 角色类型，参见 RoleTypeEnum 枚举类
     * 例如：1
     */
    private Integer type;

    /**
     * 备注
     * 例如：我是一个角色
     */
    private String remark;

    /**
     * 数据范围，参见 DataScopeEnum 枚举类
     * 例如：1
     */
    @ExcelProperty(value = "数据范围", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.DATA_SCOPE)
    private Integer dataScope;

    /**
     * 数据范围(指定部门数组)
     * 例如：1
     */
    private Set<Long> dataScopeDeptIds;

    /**
     * 创建时间
     * 例如：时间戳格式
     */
    private LocalDateTime createTime;

}
