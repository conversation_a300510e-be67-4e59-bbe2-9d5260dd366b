package com.qudaiji.cloud.p.module.system.controller.admin.auth.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 管理后台 - 登录 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthLoginResponse {

    /**
     * 用户编号
     * 必填：是
     * 示例：1024
     */
    private Long userId;

    /**
     * 访问令牌
     * 必填：是
     * 示例：happy
     */
    private String accessToken;

    /**
     * 刷新令牌
     * 必填：是
     * 示例：nice
     */
    private String refreshToken;

    /**
     * 过期时间
     * 必填：是
     */
    private LocalDateTime expiresTime;

}
