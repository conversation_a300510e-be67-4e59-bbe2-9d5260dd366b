package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理后台 - 【开放接口】访问令牌 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2OpenAccessTokenResponse {

    /**
     * 访问令牌
     * 必填，例如：tudou
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     * 必填，例如：nice
     */
    @JsonProperty("refresh_token")
    private String refreshToken;

    /**
     * 令牌类型
     * 必填，例如：bearer
     */
    @JsonProperty("token_type")
    private String tokenType;

    /**
     * 过期时间,单位：秒
     * 必填，例如：42430
     */
    @JsonProperty("expires_in")
    private Long expiresIn;

    /**
     * 授权范围,如果多个授权范围，使用空格分隔
     * 例如：user_info
     */
    private String scope;

}
