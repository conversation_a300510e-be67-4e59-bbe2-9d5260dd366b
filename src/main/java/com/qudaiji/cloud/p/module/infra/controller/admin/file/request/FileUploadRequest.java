package com.qudaiji.cloud.p.module.infra.controller.admin.file.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 管理后台 - 上传文件 Request
 */
@Data
public class FileUploadRequest {

    /**
     * 文件附件
     * 必填：是
     */
    @NotNull(message = "文件附件不能为空")
    private MultipartFile file;

    /**
     * 文件附件
     * 示例：jqm-p-serviceyuanma.png
     */
    private String path;

}
