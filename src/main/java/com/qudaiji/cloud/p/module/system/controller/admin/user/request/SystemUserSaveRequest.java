package com.qudaiji.cloud.p.module.system.controller.admin.user.request;

import cn.hutool.core.util.ObjectUtil;
import com.qudaiji.cloud.framework.common.validation.Mobile;
import com.qudaiji.cloud.p.module.system.framework.operatelog.core.DeptParseFunction;
import com.qudaiji.cloud.p.module.system.framework.operatelog.core.PostParseFunction;
import com.qudaiji.cloud.p.module.system.framework.operatelog.core.SexParseFunction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Set;

/**
 * 管理后台 - 用户创建/修改 Request
 */
@Data
public class SystemUserSaveRequest {

    /**
     * 用户编号
     * 例如：1024
     */
    private Long id;

    /**
     * 用户账号
     * 例如：jqm-p-service
     */
    @NotBlank(message = "用户账号不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    @DiffLogField(name = "用户账号")
    private String username;

    /**
     * 用户昵称
     * 例如：芋艿
     */
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    @DiffLogField(name = "用户昵称")
    private String nickname;

    /**
     * 备注
     * 例如：我是一个用户
     */
    @DiffLogField(name = "备注")
    private String remark;

    /**
     * 部门编号
     * 例如：我是一个用户
     */
    @DiffLogField(name = "部门", function = DeptParseFunction.NAME)
    private Long deptId;

    /**
     * 岗位编号数组
     * 例如：1
     */
    @DiffLogField(name = "岗位", function = PostParseFunction.NAME)
    private Set<Long> postIds;

    /**
     * 用户邮箱
     * 例如：<EMAIL>
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    @DiffLogField(name = "用户邮箱")
    private String email;

    /**
     * 手机号码
     * 例如：15601691300
     */
    @Mobile
    @DiffLogField(name = "手机号码")
    private String mobile;

    /**
     * 用户性别，参见 SexEnum 枚举类
     * 例如：1
     */
    @DiffLogField(name = "用户性别", function = SexParseFunction.NAME)
    private Integer sex;

    /**
     * 用户头像
     * 例如：https://www.iocoder.cn/xxx.png
     */
    @DiffLogField(name = "用户头像")
    private String avatar;

    // ========== 仅【创建】时，需要传递的字段 ==========

    /**
     * 密码
     * 例如：123456
     */
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password;

    @AssertTrue(message = "密码不能为空")
    @JsonIgnore
    public boolean isPasswordValid() {
        // 修改时，不需要传递
        return id != null
                // 新增时，必须都传递 password
                || (ObjectUtil.isAllNotEmpty(password));
    }

}
