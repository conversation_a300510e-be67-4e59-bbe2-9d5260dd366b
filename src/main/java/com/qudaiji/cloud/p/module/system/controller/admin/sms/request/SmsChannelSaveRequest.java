package com.qudaiji.cloud.p.module.system.controller.admin.sms.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

/**
 * 管理后台 - 短信渠道创建/修改 Request
 */
@Data
public class SmsChannelSaveRequest {

    /**
     * 编号
     * 示例：1024
     */
    private Long id;

    /**
     * 短信签名
     * 示例：李卓伦
     */
    @NotNull(message = "短信签名不能为空")
    private String signature;

    /**
     * 渠道编码，参见 SmsChannelEnum 枚举类
     * 示例：YUN_PIAN
     */
    @NotNull(message = "渠道编码不能为空")
    private String code;

    /**
     * 启用状态
     * 示例：1
     */
    @NotNull(message = "启用状态不能为空")
    private Integer status;

    /**
     * 备注
     * 示例：好吃！
     */
    private String remark;

    /**
     * 短信 API 的账号
     * 示例：jqm-p-service
     */
    @NotNull(message = "短信 API 的账号不能为空")
    private String apiKey;

    /**
     * 短信 API 的密钥
     * 示例：yuanma
     */
    private String apiSecret;

    /**
     * 短信发送回调 URL
     * 示例：http://www.iocoder.cn
     */
    @URL(message = "回调 URL 格式不正确")
    private String callbackUrl;

}
