package com.qudaiji.cloud.p.module.system.controller.admin.user.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import com.qudaiji.cloud.p.common.constants.enums.SystemDictTypeConstants;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 管理后台 - 用户信息 Response
 */
@Data
@ExcelIgnoreUnannotated
public class UserResponse {

    /**
     * 用户编号
     * 例如：1
     */
    @ExcelProperty("用户编号")
    private Long id;

    /**
     * 用户账号
     * 例如：jqm-p-service
     */
    @ExcelProperty("用户名称")
    private String username;

    /**
     * 用户昵称
     * 例如：芋艿
     */
    @ExcelProperty("用户昵称")
    private String nickname;

    /**
     * 备注
     * 例如：我是一个用户
     */
    private String remark;

    /**
     * 部门ID
     * 例如：我是一个用户
     */
    private Long deptId;
    /**
     * 部门名称
     * 例如：IT 部
     */
    @ExcelProperty("部门名称")
    private String deptName;

    /**
     * 岗位编号数组
     * 例如：1
     */
    private Set<Long> postIds;

    /**
     * 用户邮箱
     * 例如：<EMAIL>
     */
    @ExcelProperty("用户邮箱")
    private String email;

    /**
     * 手机号码
     * 例如：15601691300
     */
    @ExcelProperty("手机号码")
    private String mobile;

    /**
     * 用户性别，参见 SexEnum 枚举类
     * 例如：1
     */
    @ExcelProperty(value = "用户性别", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.USER_SEX)
    private Integer sex;

    /**
     * 用户头像
     * 例如：https://www.iocoder.cn/xxx.png
     */
    private String avatar;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     * 例如：1
     */
    @ExcelProperty(value = "帐号状态", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.COMMON_STATUS)
    private Integer status;

    /**
     * 最后登录 IP
     * 例如：***********
     */
    @ExcelProperty("最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     * 例如：时间戳格式
     */
    @ExcelProperty("最后登录时间")
    private LocalDateTime loginDate;

    /**
     * 创建时间
     * 例如：时间戳格式
     */
    private LocalDateTime createTime;

}
