package com.qudaiji.cloud.p.module.system.controller.admin.permission;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.PermissionAssignRoleDataScopeRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.PermissionAssignRoleMenuRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.PermissionAssignUserRoleRequest;
import com.qudaiji.cloud.p.module.system.service.PermissionService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 权限 Controller，提供赋予用户、角色的权限的 API 接口
 * 管理后台 - 权限
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;


    /**
     * 获得角色拥有的菜单编号
     *
     * <AUTHOR>
     * @date 2025/6/12 15:30
     **/
    @GetMapping("/list-role-menus")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-menu')")
    public CommonResult<Set<Long>> getRoleMenuList(Long roleId) {
        return success(permissionService.getRoleMenuListByRoleId(roleId));
    }

    /**
     * 赋予角色菜单
     *
     * <AUTHOR>
     * @date 2025/6/12 15:30
     **/
    @PostMapping("/assign-role-menu")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-menu')")
    public CommonResult<Boolean> assignRoleMenu(@Validated @RequestBody PermissionAssignRoleMenuRequest request) {

        // 执行菜单的分配
        permissionService.assignRoleMenu(request.getRoleId(), request.getMenuIds());
        return success(true);
    }

    /**
     * 赋予角色数据权限
     *
     * <AUTHOR>
     * @date 2025/6/12 15:30
     **/
    @PostMapping("/assign-role-data-scope")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-data-scope')")
    public CommonResult<Boolean> assignRoleDataScope(@Valid @RequestBody PermissionAssignRoleDataScopeRequest request) {
        permissionService.assignRoleDataScope(request.getRoleId(), request.getDataScope(), request.getDataScopeDeptIds());
        return success(true);
    }

    /**
     * 获得管理员拥有的角色编号列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:30
     **/
    @GetMapping("/list-user-roles")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Set<Long>> listAdminRoles(@RequestParam("userId") Long userId) {
        return success(permissionService.getUserRoleIdListByUserId(userId));
    }

    /**
     * 赋予用户角色
     *
     * <AUTHOR>
     * @date 2025/6/12 15:30
     **/
    @PostMapping("/assign-user-role")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-user-role')")
    public CommonResult<Boolean> assignUserRole(@Validated @RequestBody PermissionAssignUserRoleRequest request) {
        permissionService.assignUserRole(request.getUserId(), request.getRoleIds());
        return success(true);
    }

}
