package com.qudaiji.cloud.p.module.system.controller.admin.socail;

import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialUserBindRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialUserPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.response.SocialUserResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialUserUnbindRequest;
import com.qudaiji.cloud.p.module.system.convert.social.SocialUserConvert;
import com.qudaiji.cloud.p.module.system.entity.SocialUserDO;
import com.qudaiji.cloud.p.module.system.service.SocialUserService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 社交用户
 *
 * <AUTHOR>
 * @date 2025/6/12 15:10
 **/
@RestController
@RequestMapping("/system/social-user")
@Validated
public class SocialUserController {

    @Resource
    private SocialUserService socialUserService;

    /**
     * 社交绑定，使用 code 授权码
     *
     * <AUTHOR>
     * @date 2025/6/12 15:10
     **/
    @PostMapping("/bind")
    public CommonResult<Boolean> socialBind(@RequestBody @Valid SocialUserBindRequest request) {
        socialUserService.bindSocialUser(SocialUserConvert.INSTANCE.convert(
                getLoginUserId(), UserTypeEnum.ADMIN.getValue(), request));
        return CommonResult.success(true);
    }

    /**
     * 取消社交绑定
     *
     * <AUTHOR>
     * @date 2025/6/12 15:10
     **/
    @DeleteMapping("/unbind")
    public CommonResult<Boolean> socialUnbind(@RequestBody SocialUserUnbindRequest request) {
        socialUserService.unbindSocialUser(getLoginUserId(), UserTypeEnum.ADMIN.getValue(), request.getType(), request.getOpenid());
        return CommonResult.success(true);
    }

    // ==================== 社交用户 CRUD ====================

    /**
     * 获得社交用户
     *
     * <AUTHOR>
     * @date 2025/6/12 15:10
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:social-user:query')")
    public CommonResult<SocialUserResponse> getSocialUser(@RequestParam("id") Long id) {
        SocialUserDO socialUser = socialUserService.getSocialUser(id);
        return success(BeanUtils.toBean(socialUser, SocialUserResponse.class));
    }

    /**
     * 获得社交用户分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:11
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:social-user:query')")
    public CommonResult<PageResult<SocialUserResponse>> getSocialUserPage(@Valid SocialUserPageRequest pageVO) {
        PageResult<SocialUserDO> pageResult = socialUserService.getSocialUserPage(pageVO);
        return success(BeanUtils.toBean(pageResult, SocialUserResponse.class));
    }

}
