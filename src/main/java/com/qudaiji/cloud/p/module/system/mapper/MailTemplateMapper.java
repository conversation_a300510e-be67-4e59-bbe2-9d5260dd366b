package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.request.MailTemplatePageRequest;
import com.qudaiji.cloud.p.module.system.entity.MailTemplateDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MailTemplateMapper extends BaseMapperX<MailTemplateDO> {

    default PageResult<MailTemplateDO> selectPage(MailTemplatePageRequest request){
        return selectPage(request , new LambdaQueryWrapperX<MailTemplateDO>()
                .eqIfPresent(MailTemplateDO::getStatus, request.getStatus())
                .likeIfPresent(MailTemplateDO::getCode, request.getCode())
                .likeIfPresent(MailTemplateDO::getName, request.getName())
                .eqIfPresent(MailTemplateDO::getAccountId, request.getAccountId())
                .betweenIfPresent(MailTemplateDO::getCreateTime, request.getCreateTime()));
    }

    default Long selectCountByAccountId(Long accountId) {
        return selectCount(MailTemplateDO::getAccountId, accountId);
    }

    default MailTemplateDO selectByCode(String code) {
        return selectOne(MailTemplateDO::getCode, code);
    }

}
