package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.SmsLogDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SmsLogMapper extends BaseMapperX<SmsLogDO> {

    default PageResult<SmsLogDO> selectPage(SmsLogPageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<SmsLogDO>()
                .eqIfPresent(SmsLogDO::getChannelId, request.getChannelId())
                .eqIfPresent(SmsLogDO::getTemplateId, request.getTemplateId())
                .likeIfPresent(SmsLogDO::getMobile, request.getMobile())
                .eqIfPresent(SmsLogDO::getSendStatus, request.getSendStatus())
                .betweenIfPresent(SmsLogDO::getSendTime, request.getSendTime())
                .eqIfPresent(SmsLogDO::getReceiveStatus, request.getReceiveStatus())
                .betweenIfPresent(SmsLogDO::getReceiveTime, request.getReceiveTime())
                .orderByDesc(SmsLogDO::getId));
    }

}
