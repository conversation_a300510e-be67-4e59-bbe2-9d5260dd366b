package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.request;

import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.framework.common.util.json.JsonUtils;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.URL;

import java.util.List;

/**
 * 管理后台 - OAuth2 客户端创建/修改 Request
 */
@Data
public class OAuth2ClientSaveRequest {

    /**
     * 编号
     * 例如：1024
     */
    private Long id;

    /**
     * 客户端编号
     * 必填，例如：tudou
     */
    @NotNull(message = "客户端编号不能为空")
    private String clientId;

    /**
     * 客户端密钥
     * 必填，例如：fan
     */
    @NotNull(message = "客户端密钥不能为空")
    private String secret;

    /**
     * 应用名
     * 必填，例如：土豆
     */
    @NotNull(message = "应用名不能为空")
    private String name;

    /**
     * 应用图标
     * 必填，例如：https://www.iocoder.cn/xx.png
     */
    @NotNull(message = "应用图标不能为空")
    @URL(message = "应用图标的地址不正确")
    private String logo;

    /**
     * 应用描述
     * 例如：我是一个应用
     */
    private String description;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 必填，例如：1
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 访问令牌的有效期
     * 必填，例如：8640
     */
    @NotNull(message = "访问令牌的有效期不能为空")
    private Integer accessTokenValiditySeconds;

    /**
     * 刷新令牌的有效期
     * 必填，例如：8640000
     */
    @NotNull(message = "刷新令牌的有效期不能为空")
    private Integer refreshTokenValiditySeconds;

    /**
     * 可重定向的 URI 地址
     * 必填，例如：https://www.iocoder.cn
     */
    @NotNull(message = "可重定向的 URI 地址不能为空")
    private List<@NotEmpty(message = "重定向的 URI 不能为空") @URL(message = "重定向的 URI 格式不正确") String> redirectUris;

    /**
     * 授权类型，参见 OAuth2GrantTypeEnum 枚举
     * 必填，例如：password
     */
    @NotNull(message = "授权类型不能为空")
    private List<String> authorizedGrantTypes;

    /**
     * 授权范围
     * 例如：user_info
     */
    private List<String> scopes;

    /**
     * 自动通过的授权范围
     * 例如：user_info
     */
    private List<String> autoApproveScopes;

    /**
     * 权限
     * 例如：system:user:query
     */
    private List<String> authorities;

    /**
     * 资源
     * 例如：1024
     */
    private List<String> resourceIds;

    /**
     * 附加信息
     * 例如：{yunai: true}
     */
    private String additionalInformation;

    @AssertTrue(message = "附加信息必须是 JSON 格式")
    public boolean isAdditionalInformationJson() {
        return StrUtil.isEmpty(additionalInformation) || JsonUtils.isJson(additionalInformation);
    }

}
