package com.qudaiji.cloud.p.module.system.controller.admin.logger.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.qudaiji.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 登录日志分页列表 Request
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginLogPageRequest extends PageParam {

    /**
     * 用户 IP，模拟匹配
     */
    private String userIp;

    /**
     * 用户账号，模拟匹配
     */
    private String username;

    /**
     * 操作状态
     */
    private Boolean status;

    /**
     * 登录时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
