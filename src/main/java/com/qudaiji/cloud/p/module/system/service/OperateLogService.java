package com.qudaiji.cloud.p.module.system.service;

import com.qudaiji.cloud.framework.common.pojo.PageResult;

import com.qudaiji.cloud.p.module.system.api.logger.dto.OperateLogCreateReqDTO;
import com.qudaiji.cloud.p.module.system.api.logger.dto.OperateLogPageReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.request.OperateLogPageRequest;
import com.qudaiji.cloud.p.module.system.entity.OperateLogDO;

/**
 * 操作日志 Service 接口
 *
 * <AUTHOR>
 */
public interface OperateLogService {

    /**
     * 记录操作日志
     *
     * @param createReqDTO 创建请求
     */
    void createOperateLog(OperateLogCreateReqDTO createReqDTO);

    /**
     * 获得操作日志分页列表
     *
     * @param request 分页条件
     * @return 操作日志分页列表
     */
    PageResult<OperateLogDO> getOperateLogPage(OperateLogPageRequest request);

    /**
     * 获得操作日志分页列表
     *
     * @param pageReqDTO 分页条件
     * @return 操作日志分页列表
     */
    PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO);

}
