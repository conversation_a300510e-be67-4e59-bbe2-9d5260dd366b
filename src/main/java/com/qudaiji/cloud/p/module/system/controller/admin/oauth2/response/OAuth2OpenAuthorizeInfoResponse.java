package com.qudaiji.cloud.p.module.system.controller.admin.oauth2.response;

import com.qudaiji.cloud.framework.common.core.KeyValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 管理后台 - 授权页的信息 Response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2OpenAuthorizeInfoResponse {

    /**
     * 客户端
     */
    private Client client;

    /**
     * scope 的选中信息,使用 List 保证有序性，Key 是 scope，Value 为是否选中
     * 必填
     */
    private List<KeyValue<String, Boolean>> scopes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Client {

        /**
         * 应用名
         * 必填，例如：土豆
         */
        private String name;

        /**
         * 应用图标
         * 必填，例如：https://www.iocoder.cn/xx.png
         */
        private String logo;

    }

}
