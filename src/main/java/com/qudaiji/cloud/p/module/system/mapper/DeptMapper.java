package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.request.DeptListRequest;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface DeptMapper extends BaseMapperX<DeptDO> {

    default List<DeptDO> selectList(DeptListRequest request) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                .likeIfPresent(DeptDO::getName, request.getName())
                .eqIfPresent(DeptDO::getStatus, request.getStatus()));
    }

    default DeptDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(DeptDO::getParentId, parentId, DeptDO::getName, name);
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(DeptDO::getParentId, parentId);
    }

    default List<DeptDO> selectListByParentId(Collection<Long> parentIds) {
        return selectList(DeptDO::getParentId, parentIds);
    }

    default List<DeptDO> selectListByLeaderUserId(Long id) {
        return selectList(DeptDO::getLeaderUserId, id);
    }

}
