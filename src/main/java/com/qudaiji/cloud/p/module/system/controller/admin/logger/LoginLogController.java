package com.qudaiji.cloud.p.module.system.controller.admin.logger;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.request.LoginLogPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.response.LoginLogResponse;
import com.qudaiji.cloud.p.module.system.entity.LoginLogDO;
import com.qudaiji.cloud.p.module.system.service.LoginLogService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/system/login-log")
@Validated
public class LoginLogController {

    @Resource
    private LoginLogService loginLogService;

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:login-log:query')")
    public CommonResult<PageResult<LoginLogResponse>> getLoginLogPage(@Valid LoginLogPageRequest request) {
        PageResult<LoginLogDO> pageResult = loginLogService.getLoginLogPage(request);
        return success(BeanUtils.toBean(pageResult, LoginLogResponse.class));
    }

    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:login-log:export')")
    public void exportLoginLog(HttpServletResponse response, @Valid LoginLogPageRequest request) throws IOException {
        request.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LoginLogDO> list = loginLogService.getLoginLogPage(request).getList();
        // 输出
        ExcelUtils.write(response, "登录日志.xls", "数据列表", LoginLogResponse.class,
                BeanUtils.toBean(list, LoginLogResponse.class));
    }

}
