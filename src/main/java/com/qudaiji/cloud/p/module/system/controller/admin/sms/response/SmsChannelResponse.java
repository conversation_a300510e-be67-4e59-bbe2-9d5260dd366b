package com.qudaiji.cloud.p.module.system.controller.admin.sms.response;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import java.time.LocalDateTime;

/**
 * 管理后台 - 短信渠道 Response
 */
@Data
public class SmsChannelResponse {

    /**
     * 编号
     * 示例值：1024
     */
    private Long id;

    /**
     * 短信签名
     * 示例值：李卓伦
     */
    @NotNull(message = "短信签名不能为空")
    private String signature;

    /**
     * 渠道编码，参见 SmsChannelEnum 枚举类
     * 示例值：YUN_PIAN
     */
    private String code;

    /**
     * 启用状态
     * 示例值：1
     */
    @NotNull(message = "启用状态不能为空")
    private Integer status;

    /**
     * 备注
     * 示例值：好吃！
     */
    private String remark;

    /**
     * 短信 API 的账号
     * 示例值：jqm-p-service
     */
    @NotNull(message = "短信 API 的账号不能为空")
    private String apiKey;

    /**
     * 短信 API 的密钥
     * 示例值：yuanma
     */
    private String apiSecret;

    /**
     * 短信发送回调 URL
     * 示例值：https://www.iocoder.cn
     */
    @URL(message = "回调 URL 格式不正确")
    private String callbackUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
