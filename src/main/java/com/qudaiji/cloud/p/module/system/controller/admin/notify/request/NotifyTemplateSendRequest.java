package com.qudaiji.cloud.p.module.system.controller.admin.notify.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 管理后台 - 站内信模板的发送 Request
 */
@Data
public class NotifyTemplateSendRequest {

    /**
     * 用户id
     * 例如：01
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 用户类型
     * 例如：1
     */
    @NotNull(message = "用户类型不能为空")
    private Integer userType;

    /**
     * 模板编码
     * 例如：01
     */
    @NotEmpty(message = "模板编码不能为空")
    private String templateCode;

    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;

}
