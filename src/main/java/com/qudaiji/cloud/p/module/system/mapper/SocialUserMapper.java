package com.qudaiji.cloud.p.module.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialUserPageRequest;
import com.qudaiji.cloud.p.module.system.entity.SocialUserDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SocialUserMapper extends BaseMapperX<SocialUserDO> {

    default SocialUserDO selectByTypeAndCodeAnState(Integer type, String code, String state) {
        return selectOne(new LambdaQueryWrapper<SocialUserDO>()
                .eq(SocialUserDO::getType, type)
                .eq(SocialUserDO::getCode, code)
                .eq(SocialUserDO::getState, state));
    }

    default SocialUserDO selectByTypeAndOpenid(Integer type, String openid) {
        return selectOne(new LambdaQueryWrapper<SocialUserDO>()
                .eq(SocialUserDO::getType, type)
                .eq(SocialUserDO::getOpenid, openid));
    }

    default PageResult<SocialUserDO> selectPage(SocialUserPageRequest request) {
        return selectPage(request, new LambdaQueryWrapperX<SocialUserDO>()
                .eqIfPresent(SocialUserDO::getType, request.getType())
                .likeIfPresent(SocialUserDO::getNickname, request.getNickname())
                .likeIfPresent(SocialUserDO::getOpenid, request.getOpenid())
                .betweenIfPresent(SocialUserDO::getCreateTime, request.getCreateTime())
                .orderByDesc(SocialUserDO::getId));
    }

}
