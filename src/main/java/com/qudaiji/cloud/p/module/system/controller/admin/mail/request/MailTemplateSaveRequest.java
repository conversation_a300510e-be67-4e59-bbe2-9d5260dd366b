package com.qudaiji.cloud.p.module.system.controller.admin.mail.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 管理后台 - 邮件模版创建/修改 Request
 */
@Data
public class MailTemplateSaveRequest {

    /**
     * 编号
     * 示例值：1024
     */
    private Long id;

    /**
     * 模版名称
     * 示例值：测试名字
     */
    @NotNull(message = "名称不能为空")
    private String name;

    /**
     * 模版编号
     * 示例值：test
     */
    @NotNull(message = "模版编号不能为空")
    private String code;

    /**
     * 发送的邮箱账号编号
     * 示例值：1
     */
    @NotNull(message = "发送的邮箱账号编号不能为空")
    private Long accountId;

    /**
     * 发送人名称
     * 示例值：芋头
     */
    private String nickname;

    /**
     * 标题
     * 示例值：注册成功
     */
    @NotEmpty(message = "标题不能为空")
    private String title;

    /**
     * 内容
     * 示例值：你好，注册成功啦
     */
    @NotEmpty(message = "内容不能为空")
    private String content;

    /**
     * 状态，参见 CommonStatusEnum 枚举
     * 示例值：1
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     * 示例值：奥特曼
     */
    private String remark;

}
