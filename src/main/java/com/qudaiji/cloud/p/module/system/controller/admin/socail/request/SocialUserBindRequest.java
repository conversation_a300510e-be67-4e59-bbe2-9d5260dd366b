package com.qudaiji.cloud.p.module.system.controller.admin.socail.request;

import com.qudaiji.cloud.framework.common.validation.InEnum;
import com.qudaiji.cloud.p.common.constants.enums.social.SocialTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理后台 - 社交绑定 Request，使用 code 授权码
 *
 * <AUTHOR>
 * @date 2025/6/12 15:11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SocialUserBindRequest {

    /**
     * 社交平台的类型，参见 UserSocialTypeEnum 枚举值
     **/
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平台的类型不能为空")
    private Integer type;
    /**
     * 授权码
     **/
    @NotEmpty(message = "授权码不能为空")
    private String code;
    /**
     * state
     * 9b2ffbc1-7425-4155-9894-9d5c08541d62
     **/
    @NotEmpty(message = "state 不能为空")
    private String state;

}
