package com.qudaiji.cloud.p.module.system.convert.social;

import com.qudaiji.cloud.p.module.system.api.social.dto.SocialUserBindReqDTO;
import com.qudaiji.cloud.p.module.system.controller.admin.socail.request.SocialUserBindRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SocialUserConvert {

    SocialUserConvert INSTANCE = Mappers.getMapper(SocialUserConvert.class);

    @Mapping(source = "request.type", target = "socialType")
    SocialUserBindReqDTO convert(Long userId, Integer userType, SocialUserBindRequest request);

}
