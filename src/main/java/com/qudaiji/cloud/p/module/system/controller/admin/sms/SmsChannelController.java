package com.qudaiji.cloud.p.module.system.controller.admin.sms;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.response.SmsChannelResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.request.SmsChannelSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.response.SmsChannelSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.SmsChannelDO;
import com.qudaiji.cloud.p.module.system.service.SmsChannelService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 短信渠道
 *
 * <AUTHOR>
 * @date 2025/6/12 16:08
 **/
@RestController
@RequestMapping("system/sms-channel")
public class SmsChannelController {

    @Resource
    private SmsChannelService smsChannelService;

    /**
     * 创建短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:create')")
    public CommonResult<Long> createSmsChannel(@Valid @RequestBody SmsChannelSaveRequest request) {
        return success(smsChannelService.createSmsChannel(request));
    }

    /**
     * 更新短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:update')")
    public CommonResult<Boolean> updateSmsChannel(@Valid @RequestBody SmsChannelSaveRequest request) {
        smsChannelService.updateSmsChannel(request);
        return success(true);
    }

    /**
     * 删除短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:delete')")
    public CommonResult<Boolean> deleteSmsChannel(@RequestParam("id") Long id) {
        smsChannelService.deleteSmsChannel(id);
        return success(true);
    }

    /**
     * 获得短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:query')")
    public CommonResult<SmsChannelResponse> getSmsChannel(@RequestParam("id") Long id) {
        SmsChannelDO channel = smsChannelService.getSmsChannel(id);
        return success(BeanUtils.toBean(channel, SmsChannelResponse.class));
    }

    /**
     * 获得短信渠道分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:query')")
    public CommonResult<PageResult<SmsChannelResponse>> getSmsChannelPage(@Valid SmsChannelPageRequest request) {
        PageResult<SmsChannelDO> pageResult = smsChannelService.getSmsChannelPage(request);
        return success(BeanUtils.toBean(pageResult, SmsChannelResponse.class));
    }

    /**
     * 获得短信渠道精简列表
     * 包含被禁用的短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping({"/list-all-simple", "/simple-list"})
    public CommonResult<List<SmsChannelSimpleResponse>> getSimpleSmsChannelList() {
        List<SmsChannelDO> list = smsChannelService.getSmsChannelList();
        list.sort(Comparator.comparing(SmsChannelDO::getId));
        return success(BeanUtils.toBean(list, SmsChannelSimpleResponse.class));
    }

}
