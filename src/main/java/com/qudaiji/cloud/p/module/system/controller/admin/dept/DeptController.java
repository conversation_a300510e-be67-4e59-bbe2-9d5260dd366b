package com.qudaiji.cloud.p.module.system.controller.admin.dept;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.request.DeptListRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.DeptResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.request.DeptSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.DeptSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.service.DeptService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/system/dept")
@Validated
public class DeptController {

    @Resource
    private DeptService deptService;

    /**
     * 创建部门
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @PostMapping("create")
    @PreAuthorize("@ss.hasPermission('system:dept:create')")
    public CommonResult<Long> createDept(@Valid @RequestBody DeptSaveRequest request) {
        Long deptId = deptService.createDept(request);
        return success(deptId);
    }

    /**
     * 更新部门
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @PutMapping("update")
    @PreAuthorize("@ss.hasPermission('system:dept:update')")
    public CommonResult<Boolean> updateDept(@Valid @RequestBody DeptSaveRequest request) {
        deptService.updateDept(request);
        return success(true);
    }

    /**
     * 删除部门
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @DeleteMapping("delete")
    @PreAuthorize("@ss.hasPermission('system:dept:delete')")
    public CommonResult<Boolean> deleteDept(@RequestParam("id") Long id) {
        deptService.deleteDept(id);
        return success(true);
    }

    /**
     * 获取部门列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<List<DeptResponse>> getDeptList(DeptListRequest request) {
        List<DeptDO> list = deptService.getDeptList(request);
        return success(BeanUtils.toBean(list, DeptResponse.class));
    }

    /**
     * 获取部门精简信息列表
     * 只包含被开启的部门，主要用于前端的下拉选项
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @GetMapping(value = {"/list-all-simple", "/simple-list"})
    public CommonResult<List<DeptSimpleResponse>> getSimpleDeptList() {
        List<DeptDO> list = deptService.getDeptList(
                new DeptListRequest().setStatus(CommonStatusEnum.ENABLE.getStatus()));
        return success(BeanUtils.toBean(list, DeptSimpleResponse.class));
    }

    /**
     * 获得部门信息
     *
     * <AUTHOR>
     * @date 2025/6/12 15:08
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:dept:query')")
    public CommonResult<DeptResponse> getDept(@RequestParam("id") Long id) {
        DeptDO dept = deptService.getDept(id);
        return success(BeanUtils.toBean(dept, DeptResponse.class));
    }

}
