package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.request.MenuListRequest;
import com.qudaiji.cloud.p.module.system.entity.MenuDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MenuMapper extends BaseMapperX<MenuDO> {

    default MenuDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(MenuDO::getParentId, parentId, MenuDO::getName, name);
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(MenuDO::getParentId, parentId);
    }

    default List<MenuDO> selectList(MenuListRequest request) {
        return selectList(new LambdaQueryWrapperX<MenuDO>()
                .likeIfPresent(MenuDO::getName, request.getName())
                .eqIfPresent(MenuDO::getStatus, request.getStatus()));
    }

    default List<MenuDO> selectListByPermission(String permission) {
        return selectList(MenuDO::getPermission, permission);
    }
}
