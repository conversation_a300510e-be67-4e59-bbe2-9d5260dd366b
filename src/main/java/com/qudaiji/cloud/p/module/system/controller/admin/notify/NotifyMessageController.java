package com.qudaiji.cloud.p.module.system.controller.admin.notify;

import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyMessageMyPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.request.NotifyMessagePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.notify.response.NotifyMessageResponse;
import com.qudaiji.cloud.p.module.system.entity.NotifyMessageDO;
import com.qudaiji.cloud.p.module.system.service.NotifyMessageService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 我的站内信
 *
 * <AUTHOR>
 * @date 2025/6/12 15:39
 **/
@RestController
@RequestMapping("/system/notify-message")
@Validated
public class NotifyMessageController {

    @Resource
    private NotifyMessageService notifyMessageService;

    // ========== 管理所有的站内信 ==========

    /**
     * 获得站内信
     *
     * <AUTHOR>
     * @date 2025/6/12 15:39
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:notify-message:query')")
    public CommonResult<NotifyMessageResponse> getNotifyMessage(@RequestParam("id") Long id) {
        NotifyMessageDO message = notifyMessageService.getNotifyMessage(id);
        return success(BeanUtils.toBean(message, NotifyMessageResponse.class));
    }

    /**
     * 获得站内信分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:39
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:notify-message:query')")
    public CommonResult<PageResult<NotifyMessageResponse>> getNotifyMessagePage(@Valid NotifyMessagePageRequest pageVO) {
        PageResult<NotifyMessageDO> pageResult = notifyMessageService.getNotifyMessagePage(pageVO);
        return success(BeanUtils.toBean(pageResult, NotifyMessageResponse.class));
    }

    // ========== 查看自己的站内信 ==========

    /**
     * 获得我的站内信分页
     *
     * <AUTHOR>
     * @date 2025/6/12 15:42
     **/
    @GetMapping("/my-page")
    public CommonResult<PageResult<NotifyMessageResponse>> getMyMyNotifyMessagePage(@Valid NotifyMessageMyPageRequest pageVO) {
        PageResult<NotifyMessageDO> pageResult = notifyMessageService.getMyMyNotifyMessagePage(pageVO,
                getLoginUserId(), UserTypeEnum.ADMIN.getValue());
        return success(BeanUtils.toBean(pageResult, NotifyMessageResponse.class));
    }

    /**
     * 标记站内信为已读
     *
     * <AUTHOR>
     * @date 2025/6/12 15:42
     **/
    @PutMapping("/update-read")
    public CommonResult<Boolean> updateNotifyMessageRead(@RequestParam("ids") List<Long> ids) {
        notifyMessageService.updateNotifyMessageRead(ids, getLoginUserId(), UserTypeEnum.ADMIN.getValue());
        return success(Boolean.TRUE);
    }

    /**
     * 标记所有站内信为已读
     *
     * <AUTHOR>
     * @date 2025/6/12 15:42
     **/
    @PutMapping("/update-all-read")
    public CommonResult<Boolean> updateAllNotifyMessageRead() {
        notifyMessageService.updateAllNotifyMessageRead(getLoginUserId(), UserTypeEnum.ADMIN.getValue());
        return success(Boolean.TRUE);
    }

    /**
     * 获取当前用户的最新站内信列表，默认 10 条
     *
     * <AUTHOR>
     * @date 2025/6/12 15:42
     **/
    @GetMapping("/get-unread-list")
    public CommonResult<List<NotifyMessageResponse>> getUnreadNotifyMessageList(
            @RequestParam(name = "size", defaultValue = "10") Integer size) {
        List<NotifyMessageDO> list = notifyMessageService.getUnreadNotifyMessageList(
                getLoginUserId(), UserTypeEnum.ADMIN.getValue(), size);
        return success(BeanUtils.toBean(list, NotifyMessageResponse.class));
    }

    /**
     * 获得当前用户的未读站内信数量
     *
     * <AUTHOR>
     * @date 2025/6/12 15:42
     **/
    @GetMapping("/get-unread-count")
    public CommonResult<Long> getUnreadNotifyMessageCount() {
        return success(notifyMessageService.getUnreadNotifyMessageCount(
                getLoginUserId(), UserTypeEnum.ADMIN.getValue()));
    }

}
