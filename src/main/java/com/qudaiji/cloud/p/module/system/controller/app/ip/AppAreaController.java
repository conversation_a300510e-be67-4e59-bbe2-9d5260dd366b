package com.qudaiji.cloud.p.module.system.controller.app.ip;

import cn.hutool.core.lang.Assert;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.framework.ip.core.Area;
import com.qudaiji.cloud.framework.ip.core.utils.AreaUtils;
import com.qudaiji.cloud.p.module.system.controller.app.ip.response.AppAreaNodeResponse;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 用户 App - 地区
 *
 * <AUTHOR>
 * @date 2025/6/12 16:18
 **/
@RestController
@RequestMapping("/system/area")
@Validated
public class AppAreaController {

    /**
     * 获得地区树
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @GetMapping("/tree")
    @PermitAll
    public CommonResult<List<AppAreaNodeResponse>> getAreaTree() {
        Area area = AreaUtils.getArea(Area.ID_CHINA);
        Assert.notNull(area, "获取不到中国");
        return success(BeanUtils.toBean(area.getChildren(), AppAreaNodeResponse.class));
    }

}
