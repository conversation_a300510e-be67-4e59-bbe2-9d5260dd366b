package com.qudaiji.cloud.p.module.infra.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.infra.controller.admin.file.request.FilePageRequest;
import com.qudaiji.cloud.p.module.infra.entity.FileDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageRequest pageRequest) {
        return selectPage(pageRequest, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, pageRequest.getPath())
                .likeIfPresent(FileDO::getType, pageRequest.getType())
                .betweenIfPresent(FileDO::getCreateTime, pageRequest.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

}
